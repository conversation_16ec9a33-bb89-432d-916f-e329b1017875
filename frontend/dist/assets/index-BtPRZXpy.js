var jm=Object.defineProperty;var Tm=(i,c,r)=>c in i?jm(i,c,{enumerable:!0,configurable:!0,writable:!0,value:r}):i[c]=r;var st=(i,c,r)=>Tm(i,typeof c!="symbol"?c+"":c,r);import{r as re}from"./router-C5ArLsJ8.js";import{r as Mm,a as Nm}from"./vendor-gH-7aFTg.js";import{c as Zd}from"./state-CnPNMA5m.js";(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const h of document.querySelectorAll('link[rel="modulepreload"]'))s(h);new MutationObserver(h=>{for(const g of h)if(g.type==="childList")for(const _ of g.addedNodes)_.tagName==="LINK"&&_.rel==="modulepreload"&&s(_)}).observe(document,{childList:!0,subtree:!0});function r(h){const g={};return h.integrity&&(g.integrity=h.integrity),h.referrerPolicy&&(g.referrerPolicy=h.referrerPolicy),h.crossOrigin==="use-credentials"?g.credentials="include":h.crossOrigin==="anonymous"?g.credentials="omit":g.credentials="same-origin",g}function s(h){if(h.ep)return;h.ep=!0;const g=r(h);fetch(h.href,g)}})();var xs={exports:{}},Mn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dd;function _m(){if(Dd)return Mn;Dd=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function r(s,h,g){var _=null;if(g!==void 0&&(_=""+g),h.key!==void 0&&(_=""+h.key),"key"in h){g={};for(var z in h)z!=="key"&&(g[z]=h[z])}else g=h;return h=g.ref,{$$typeof:i,type:s,key:_,ref:h!==void 0?h:null,props:g}}return Mn.Fragment=c,Mn.jsx=r,Mn.jsxs=r,Mn}var Od;function zm(){return Od||(Od=1,xs.exports=_m()),xs.exports}var d=zm(),bs={exports:{}},Nn={},ps={exports:{}},Ss={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rd;function Am(){return Rd||(Rd=1,function(i){function c(M,E){var Y=M.length;M.push(E);e:for(;0<Y;){var se=Y-1>>>1,fe=M[se];if(0<h(fe,E))M[se]=E,M[Y]=fe,Y=se;else break e}}function r(M){return M.length===0?null:M[0]}function s(M){if(M.length===0)return null;var E=M[0],Y=M.pop();if(Y!==E){M[0]=Y;e:for(var se=0,fe=M.length,Ue=fe>>>1;se<Ue;){var he=2*(se+1)-1,te=M[he],Te=he+1,yt=M[Te];if(0>h(te,Y))Te<fe&&0>h(yt,te)?(M[se]=yt,M[Te]=Y,se=Te):(M[se]=te,M[he]=Y,se=he);else if(Te<fe&&0>h(yt,Y))M[se]=yt,M[Te]=Y,se=Te;else break e}}return E}function h(M,E){var Y=M.sortIndex-E.sortIndex;return Y!==0?Y:M.id-E.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var g=performance;i.unstable_now=function(){return g.now()}}else{var _=Date,z=_.now();i.unstable_now=function(){return _.now()-z}}var O=[],D=[],A=1,K=null,G=3,Q=!1,oe=!1,C=!1,w=!1,J=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,ie=typeof setImmediate<"u"?setImmediate:null;function Fe(M){for(var E=r(D);E!==null;){if(E.callback===null)s(D);else if(E.startTime<=M)s(D),E.sortIndex=E.expirationTime,c(O,E);else break;E=r(D)}}function jt(M){if(C=!1,Fe(M),!oe)if(r(O)!==null)oe=!0,mt||(mt=!0,Ie());else{var E=r(D);E!==null&&Mt(jt,E.startTime-M)}}var mt=!1,Tt=-1,ft=5,Cl=-1;function En(){return w?!0:!(i.unstable_now()-Cl<ft)}function wl(){if(w=!1,mt){var M=i.unstable_now();Cl=M;var E=!0;try{e:{oe=!1,C&&(C=!1,$(Tt),Tt=-1),Q=!0;var Y=G;try{t:{for(Fe(M),K=r(O);K!==null&&!(K.expirationTime>M&&En());){var se=K.callback;if(typeof se=="function"){K.callback=null,G=K.priorityLevel;var fe=se(K.expirationTime<=M);if(M=i.unstable_now(),typeof fe=="function"){K.callback=fe,Fe(M),E=!0;break t}K===r(O)&&s(O),Fe(M)}else s(O);K=r(O)}if(K!==null)E=!0;else{var Ue=r(D);Ue!==null&&Mt(jt,Ue.startTime-M),E=!1}}break e}finally{K=null,G=Y,Q=!1}E=void 0}}finally{E?Ie():mt=!1}}}var Ie;if(typeof ie=="function")Ie=function(){ie(wl)};else if(typeof MessageChannel<"u"){var Dn=new MessageChannel,Na=Dn.port2;Dn.port1.onmessage=wl,Ie=function(){Na.postMessage(null)}}else Ie=function(){J(wl,0)};function Mt(M,E){Tt=J(function(){M(i.unstable_now())},E)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(M){M.callback=null},i.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ft=0<M?Math.floor(1e3/M):5},i.unstable_getCurrentPriorityLevel=function(){return G},i.unstable_next=function(M){switch(G){case 1:case 2:case 3:var E=3;break;default:E=G}var Y=G;G=E;try{return M()}finally{G=Y}},i.unstable_requestPaint=function(){w=!0},i.unstable_runWithPriority=function(M,E){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var Y=G;G=M;try{return E()}finally{G=Y}},i.unstable_scheduleCallback=function(M,E,Y){var se=i.unstable_now();switch(typeof Y=="object"&&Y!==null?(Y=Y.delay,Y=typeof Y=="number"&&0<Y?se+Y:se):Y=se,M){case 1:var fe=-1;break;case 2:fe=250;break;case 5:fe=1073741823;break;case 4:fe=1e4;break;default:fe=5e3}return fe=Y+fe,M={id:A++,callback:E,priorityLevel:M,startTime:Y,expirationTime:fe,sortIndex:-1},Y>se?(M.sortIndex=Y,c(D,M),r(O)===null&&M===r(D)&&(C?($(Tt),Tt=-1):C=!0,Mt(jt,Y-se))):(M.sortIndex=fe,c(O,M),oe||Q||(oe=!0,mt||(mt=!0,Ie()))),M},i.unstable_shouldYield=En,i.unstable_wrapCallback=function(M){var E=G;return function(){var Y=G;G=E;try{return M.apply(this,arguments)}finally{G=Y}}}}(Ss)),Ss}var Ud;function Em(){return Ud||(Ud=1,ps.exports=Am()),ps.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cd;function Dm(){if(Cd)return Nn;Cd=1;var i=Em(),c=Mm(),r=Nm();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function h(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function g(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function _(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function z(e){if(g(e)!==e)throw Error(s(188))}function O(e){var t=e.alternate;if(!t){if(t=g(e),t===null)throw Error(s(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return z(n),e;if(u===a)return z(n),t;u=u.sibling}throw Error(s(188))}if(l.return!==a.return)l=n,a=u;else{for(var f=!1,o=n.child;o;){if(o===l){f=!0,l=n,a=u;break}if(o===a){f=!0,a=n,l=u;break}o=o.sibling}if(!f){for(o=u.child;o;){if(o===l){f=!0,l=u,a=n;break}if(o===a){f=!0,a=u,l=n;break}o=o.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==a)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?e:t}function D(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=D(e),t!==null)return t;e=e.sibling}return null}var A=Object.assign,K=Symbol.for("react.element"),G=Symbol.for("react.transitional.element"),Q=Symbol.for("react.portal"),oe=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),w=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),ie=Symbol.for("react.context"),Fe=Symbol.for("react.forward_ref"),jt=Symbol.for("react.suspense"),mt=Symbol.for("react.suspense_list"),Tt=Symbol.for("react.memo"),ft=Symbol.for("react.lazy"),Cl=Symbol.for("react.activity"),En=Symbol.for("react.memo_cache_sentinel"),wl=Symbol.iterator;function Ie(e){return e===null||typeof e!="object"?null:(e=wl&&e[wl]||e["@@iterator"],typeof e=="function"?e:null)}var Dn=Symbol.for("react.client.reference");function Na(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Dn?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case oe:return"Fragment";case w:return"Profiler";case C:return"StrictMode";case jt:return"Suspense";case mt:return"SuspenseList";case Cl:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Q:return"Portal";case ie:return(e.displayName||"Context")+".Provider";case $:return(e._context.displayName||"Context")+".Consumer";case Fe:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Tt:return t=e.displayName||null,t!==null?t:Na(e.type)||"Memo";case ft:t=e._payload,e=e._init;try{return Na(e(t))}catch{}}return null}var Mt=Array.isArray,M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,E=r.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Y={pending:!1,data:null,method:null,action:null},se=[],fe=-1;function Ue(e){return{current:e}}function he(e){0>fe||(e.current=se[fe],se[fe]=null,fe--)}function te(e,t){fe++,se[fe]=e.current,e.current=t}var Te=Ue(null),yt=Ue(null),Yt=Ue(null),On=Ue(null);function Rn(e,t){switch(te(Yt,t),te(yt,e),te(Te,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?nd(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=nd(t),e=ud(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}he(Te),te(Te,e)}function Hl(){he(Te),he(yt),he(Yt)}function ai(e){e.memoizedState!==null&&te(On,e);var t=Te.current,l=ud(t,e.type);t!==l&&(te(yt,e),te(Te,l))}function Un(e){yt.current===e&&(he(Te),he(yt)),On.current===e&&(he(On),bn._currentValue=Y)}var ni=Object.prototype.hasOwnProperty,ui=i.unstable_scheduleCallback,ii=i.unstable_cancelCallback,e0=i.unstable_shouldYield,t0=i.unstable_requestPaint,vt=i.unstable_now,l0=i.unstable_getCurrentPriorityLevel,Hs=i.unstable_ImmediatePriority,Bs=i.unstable_UserBlockingPriority,Cn=i.unstable_NormalPriority,a0=i.unstable_LowPriority,qs=i.unstable_IdlePriority,n0=i.log,u0=i.unstable_setDisableYieldValue,_a=null,Ye=null;function Gt(e){if(typeof n0=="function"&&u0(e),Ye&&typeof Ye.setStrictMode=="function")try{Ye.setStrictMode(_a,e)}catch{}}var Ge=Math.clz32?Math.clz32:s0,i0=Math.log,c0=Math.LN2;function s0(e){return e>>>=0,e===0?32:31-(i0(e)/c0|0)|0}var wn=256,Hn=4194304;function ol(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Bn(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,u=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var o=a&134217727;return o!==0?(a=o&~u,a!==0?n=ol(a):(f&=o,f!==0?n=ol(f):l||(l=o&~e,l!==0&&(n=ol(l))))):(o=a&~u,o!==0?n=ol(o):f!==0?n=ol(f):l||(l=a&~e,l!==0&&(n=ol(l)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,l=t&-t,u>=l||u===32&&(l&4194048)!==0)?t:n}function za(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function f0(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ls(){var e=wn;return wn<<=1,(wn&4194048)===0&&(wn=256),e}function Ys(){var e=Hn;return Hn<<=1,(Hn&62914560)===0&&(Hn=4194304),e}function ci(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function Aa(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function r0(e,t,l,a,n,u){var f=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var o=e.entanglements,m=e.expirationTimes,b=e.hiddenUpdates;for(l=f&~l;0<l;){var j=31-Ge(l),N=1<<j;o[j]=0,m[j]=-1;var p=b[j];if(p!==null)for(b[j]=null,j=0;j<p.length;j++){var S=p[j];S!==null&&(S.lane&=-536870913)}l&=~N}a!==0&&Gs(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(f&~t))}function Gs(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-Ge(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Qs(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-Ge(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function si(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function fi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Xs(){var e=E.p;return e!==0?e:(e=window.event,e===void 0?32:Md(e.type))}function o0(e,t){var l=E.p;try{return E.p=e,t()}finally{E.p=l}}var Qt=Math.random().toString(36).slice(2),Oe="__reactFiber$"+Qt,we="__reactProps$"+Qt,Bl="__reactContainer$"+Qt,ri="__reactEvents$"+Qt,d0="__reactListeners$"+Qt,h0="__reactHandles$"+Qt,Zs="__reactResources$"+Qt,Ea="__reactMarker$"+Qt;function oi(e){delete e[Oe],delete e[we],delete e[ri],delete e[d0],delete e[h0]}function ql(e){var t=e[Oe];if(t)return t;for(var l=e.parentNode;l;){if(t=l[Bl]||l[Oe]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=fd(e);e!==null;){if(l=e[Oe])return l;e=fd(e)}return t}e=l,l=e.parentNode}return null}function Ll(e){if(e=e[Oe]||e[Bl]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Da(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function Yl(e){var t=e[Zs];return t||(t=e[Zs]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Me(e){e[Ea]=!0}var ks=new Set,Vs={};function dl(e,t){Gl(e,t),Gl(e+"Capture",t)}function Gl(e,t){for(Vs[e]=t,e=0;e<t.length;e++)ks.add(t[e])}var m0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ks={},Js={};function y0(e){return ni.call(Js,e)?!0:ni.call(Ks,e)?!1:m0.test(e)?Js[e]=!0:(Ks[e]=!0,!1)}function qn(e,t,l){if(y0(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function Ln(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Nt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var di,Ws;function Ql(e){if(di===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);di=t&&t[1]||"",Ws=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+di+e+Ws}var hi=!1;function mi(e,t){if(!e||hi)return"";hi=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(N,[])}catch(S){var p=S}Reflect.construct(e,[],N)}else{try{N.call()}catch(S){p=S}e.call(N.prototype)}}else{try{throw Error()}catch(S){p=S}(N=e())&&typeof N.catch=="function"&&N.catch(function(){})}}catch(S){if(S&&p&&typeof S.stack=="string")return[S.stack,p.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),f=u[0],o=u[1];if(f&&o){var m=f.split(`
`),b=o.split(`
`);for(n=a=0;a<m.length&&!m[a].includes("DetermineComponentFrameRoot");)a++;for(;n<b.length&&!b[n].includes("DetermineComponentFrameRoot");)n++;if(a===m.length||n===b.length)for(a=m.length-1,n=b.length-1;1<=a&&0<=n&&m[a]!==b[n];)n--;for(;1<=a&&0<=n;a--,n--)if(m[a]!==b[n]){if(a!==1||n!==1)do if(a--,n--,0>n||m[a]!==b[n]){var j=`
`+m[a].replace(" at new "," at ");return e.displayName&&j.includes("<anonymous>")&&(j=j.replace("<anonymous>",e.displayName)),j}while(1<=a&&0<=n);break}}}finally{hi=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?Ql(l):""}function v0(e){switch(e.tag){case 26:case 27:case 5:return Ql(e.type);case 16:return Ql("Lazy");case 13:return Ql("Suspense");case 19:return Ql("SuspenseList");case 0:case 15:return mi(e.type,!1);case 11:return mi(e.type.render,!1);case 1:return mi(e.type,!0);case 31:return Ql("Activity");default:return""}}function $s(e){try{var t="";do t+=v0(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Pe(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fs(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function g0(e){var t=Fs(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){a=""+f,u.call(this,f)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(f){a=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Yn(e){e._valueTracker||(e._valueTracker=g0(e))}function Is(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=Fs(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function Gn(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var x0=/[\n"\\]/g;function et(e){return e.replace(x0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function yi(e,t,l,a,n,u,f,o){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Pe(t)):e.value!==""+Pe(t)&&(e.value=""+Pe(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?vi(e,f,Pe(t)):l!=null?vi(e,f,Pe(l)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.name=""+Pe(o):e.removeAttribute("name")}function Ps(e,t,l,a,n,u,f,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;l=l!=null?""+Pe(l):"",t=t!=null?""+Pe(t):l,o||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=o?e.checked:!!a,e.defaultChecked=!!a,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function vi(e,t,l){t==="number"&&Gn(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function Xl(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+Pe(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function ef(e,t,l){if(t!=null&&(t=""+Pe(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+Pe(l):""}function tf(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(s(92));if(Mt(a)){if(1<a.length)throw Error(s(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=Pe(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function Zl(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var b0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function lf(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||b0.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function af(e,t,l){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&lf(e,n,a)}else for(var u in t)t.hasOwnProperty(u)&&lf(e,u,t[u])}function gi(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var p0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),S0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Qn(e){return S0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var xi=null;function bi(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var kl=null,Vl=null;function nf(e){var t=Ll(e);if(t&&(e=t.stateNode)){var l=e[we]||null;e:switch(e=t.stateNode,t.type){case"input":if(yi(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+et(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[we]||null;if(!n)throw Error(s(90));yi(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&Is(a)}break e;case"textarea":ef(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&Xl(e,!!l.multiple,t,!1)}}}var pi=!1;function uf(e,t,l){if(pi)return e(t,l);pi=!0;try{var a=e(t);return a}finally{if(pi=!1,(kl!==null||Vl!==null)&&(zu(),kl&&(t=kl,e=Vl,Vl=kl=null,nf(t),e)))for(t=0;t<e.length;t++)nf(e[t])}}function Oa(e,t){var l=e.stateNode;if(l===null)return null;var a=l[we]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(s(231,t,typeof l));return l}var _t=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Si=!1;if(_t)try{var Ra={};Object.defineProperty(Ra,"passive",{get:function(){Si=!0}}),window.addEventListener("test",Ra,Ra),window.removeEventListener("test",Ra,Ra)}catch{Si=!1}var Xt=null,ji=null,Xn=null;function cf(){if(Xn)return Xn;var e,t=ji,l=t.length,a,n="value"in Xt?Xt.value:Xt.textContent,u=n.length;for(e=0;e<l&&t[e]===n[e];e++);var f=l-e;for(a=1;a<=f&&t[l-a]===n[u-a];a++);return Xn=n.slice(e,1<a?1-a:void 0)}function Zn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function kn(){return!0}function sf(){return!1}function He(e){function t(l,a,n,u,f){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(l=e[o],this[o]=l?l(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?kn:sf,this.isPropagationStopped=sf,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=kn)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=kn)},persist:function(){},isPersistent:kn}),t}var hl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Vn=He(hl),Ua=A({},hl,{view:0,detail:0}),j0=He(Ua),Ti,Mi,Ca,Kn=A({},Ua,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_i,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ca&&(Ca&&e.type==="mousemove"?(Ti=e.screenX-Ca.screenX,Mi=e.screenY-Ca.screenY):Mi=Ti=0,Ca=e),Ti)},movementY:function(e){return"movementY"in e?e.movementY:Mi}}),ff=He(Kn),T0=A({},Kn,{dataTransfer:0}),M0=He(T0),N0=A({},Ua,{relatedTarget:0}),Ni=He(N0),_0=A({},hl,{animationName:0,elapsedTime:0,pseudoElement:0}),z0=He(_0),A0=A({},hl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),E0=He(A0),D0=A({},hl,{data:0}),rf=He(D0),O0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},R0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},U0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function C0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=U0[e])?!!t[e]:!1}function _i(){return C0}var w0=A({},Ua,{key:function(e){if(e.key){var t=O0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Zn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?R0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_i,charCode:function(e){return e.type==="keypress"?Zn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Zn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),H0=He(w0),B0=A({},Kn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),of=He(B0),q0=A({},Ua,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_i}),L0=He(q0),Y0=A({},hl,{propertyName:0,elapsedTime:0,pseudoElement:0}),G0=He(Y0),Q0=A({},Kn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),X0=He(Q0),Z0=A({},hl,{newState:0,oldState:0}),k0=He(Z0),V0=[9,13,27,32],zi=_t&&"CompositionEvent"in window,wa=null;_t&&"documentMode"in document&&(wa=document.documentMode);var K0=_t&&"TextEvent"in window&&!wa,df=_t&&(!zi||wa&&8<wa&&11>=wa),hf=" ",mf=!1;function yf(e,t){switch(e){case"keyup":return V0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function vf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Kl=!1;function J0(e,t){switch(e){case"compositionend":return vf(t);case"keypress":return t.which!==32?null:(mf=!0,hf);case"textInput":return e=t.data,e===hf&&mf?null:e;default:return null}}function W0(e,t){if(Kl)return e==="compositionend"||!zi&&yf(e,t)?(e=cf(),Xn=ji=Xt=null,Kl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return df&&t.locale!=="ko"?null:t.data;default:return null}}var $0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gf(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!$0[e.type]:t==="textarea"}function xf(e,t,l,a){kl?Vl?Vl.push(a):Vl=[a]:kl=a,t=Uu(t,"onChange"),0<t.length&&(l=new Vn("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var Ha=null,Ba=null;function F0(e){Po(e,0)}function Jn(e){var t=Da(e);if(Is(t))return e}function bf(e,t){if(e==="change")return t}var pf=!1;if(_t){var Ai;if(_t){var Ei="oninput"in document;if(!Ei){var Sf=document.createElement("div");Sf.setAttribute("oninput","return;"),Ei=typeof Sf.oninput=="function"}Ai=Ei}else Ai=!1;pf=Ai&&(!document.documentMode||9<document.documentMode)}function jf(){Ha&&(Ha.detachEvent("onpropertychange",Tf),Ba=Ha=null)}function Tf(e){if(e.propertyName==="value"&&Jn(Ba)){var t=[];xf(t,Ba,e,bi(e)),uf(F0,t)}}function I0(e,t,l){e==="focusin"?(jf(),Ha=t,Ba=l,Ha.attachEvent("onpropertychange",Tf)):e==="focusout"&&jf()}function P0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Jn(Ba)}function eh(e,t){if(e==="click")return Jn(t)}function th(e,t){if(e==="input"||e==="change")return Jn(t)}function lh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qe=typeof Object.is=="function"?Object.is:lh;function qa(e,t){if(Qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ni.call(t,n)||!Qe(e[n],t[n]))return!1}return!0}function Mf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Nf(e,t){var l=Mf(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Mf(l)}}function _f(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?_f(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function zf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Gn(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=Gn(e.document)}return t}function Di(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ah=_t&&"documentMode"in document&&11>=document.documentMode,Jl=null,Oi=null,La=null,Ri=!1;function Af(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Ri||Jl==null||Jl!==Gn(a)||(a=Jl,"selectionStart"in a&&Di(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),La&&qa(La,a)||(La=a,a=Uu(Oi,"onSelect"),0<a.length&&(t=new Vn("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=Jl)))}function ml(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var Wl={animationend:ml("Animation","AnimationEnd"),animationiteration:ml("Animation","AnimationIteration"),animationstart:ml("Animation","AnimationStart"),transitionrun:ml("Transition","TransitionRun"),transitionstart:ml("Transition","TransitionStart"),transitioncancel:ml("Transition","TransitionCancel"),transitionend:ml("Transition","TransitionEnd")},Ui={},Ef={};_t&&(Ef=document.createElement("div").style,"AnimationEvent"in window||(delete Wl.animationend.animation,delete Wl.animationiteration.animation,delete Wl.animationstart.animation),"TransitionEvent"in window||delete Wl.transitionend.transition);function yl(e){if(Ui[e])return Ui[e];if(!Wl[e])return e;var t=Wl[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Ef)return Ui[e]=t[l];return e}var Df=yl("animationend"),Of=yl("animationiteration"),Rf=yl("animationstart"),nh=yl("transitionrun"),uh=yl("transitionstart"),ih=yl("transitioncancel"),Uf=yl("transitionend"),Cf=new Map,Ci="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ci.push("scrollEnd");function rt(e,t){Cf.set(e,t),dl(t,[e])}var wf=new WeakMap;function tt(e,t){if(typeof e=="object"&&e!==null){var l=wf.get(e);return l!==void 0?l:(t={value:e,source:t,stack:$s(t)},wf.set(e,t),t)}return{value:e,source:t,stack:$s(t)}}var lt=[],$l=0,wi=0;function Wn(){for(var e=$l,t=wi=$l=0;t<e;){var l=lt[t];lt[t++]=null;var a=lt[t];lt[t++]=null;var n=lt[t];lt[t++]=null;var u=lt[t];if(lt[t++]=null,a!==null&&n!==null){var f=a.pending;f===null?n.next=n:(n.next=f.next,f.next=n),a.pending=n}u!==0&&Hf(l,n,u)}}function $n(e,t,l,a){lt[$l++]=e,lt[$l++]=t,lt[$l++]=l,lt[$l++]=a,wi|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function Hi(e,t,l,a){return $n(e,t,l,a),Fn(e)}function Fl(e,t){return $n(e,null,null,t),Fn(e)}function Hf(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=e.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-Ge(l),e=u.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),u):null}function Fn(e){if(50<on)throw on=0,Qc=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Il={};function ch(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xe(e,t,l,a){return new ch(e,t,l,a)}function Bi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function zt(e,t){var l=e.alternate;return l===null?(l=Xe(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Bf(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function In(e,t,l,a,n,u){var f=0;if(a=e,typeof e=="function")Bi(e)&&(f=1);else if(typeof e=="string")f=fm(e,l,Te.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Cl:return e=Xe(31,l,t,n),e.elementType=Cl,e.lanes=u,e;case oe:return vl(l.children,n,u,t);case C:f=8,n|=24;break;case w:return e=Xe(12,l,t,n|2),e.elementType=w,e.lanes=u,e;case jt:return e=Xe(13,l,t,n),e.elementType=jt,e.lanes=u,e;case mt:return e=Xe(19,l,t,n),e.elementType=mt,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case J:case ie:f=10;break e;case $:f=9;break e;case Fe:f=11;break e;case Tt:f=14;break e;case ft:f=16,a=null;break e}f=29,l=Error(s(130,e===null?"null":typeof e,"")),a=null}return t=Xe(f,l,t,n),t.elementType=e,t.type=a,t.lanes=u,t}function vl(e,t,l,a){return e=Xe(7,e,a,t),e.lanes=l,e}function qi(e,t,l){return e=Xe(6,e,null,t),e.lanes=l,e}function Li(e,t,l){return t=Xe(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Pl=[],ea=0,Pn=null,eu=0,at=[],nt=0,gl=null,At=1,Et="";function xl(e,t){Pl[ea++]=eu,Pl[ea++]=Pn,Pn=e,eu=t}function qf(e,t,l){at[nt++]=At,at[nt++]=Et,at[nt++]=gl,gl=e;var a=At;e=Et;var n=32-Ge(a)-1;a&=~(1<<n),l+=1;var u=32-Ge(t)+n;if(30<u){var f=n-n%5;u=(a&(1<<f)-1).toString(32),a>>=f,n-=f,At=1<<32-Ge(t)+n|l<<n|a,Et=u+e}else At=1<<u|l<<n|a,Et=e}function Yi(e){e.return!==null&&(xl(e,1),qf(e,1,0))}function Gi(e){for(;e===Pn;)Pn=Pl[--ea],Pl[ea]=null,eu=Pl[--ea],Pl[ea]=null;for(;e===gl;)gl=at[--nt],at[nt]=null,Et=at[--nt],at[nt]=null,At=at[--nt],at[nt]=null}var Ce=null,ye=null,I=!1,bl=null,gt=!1,Qi=Error(s(519));function pl(e){var t=Error(s(418,""));throw Qa(tt(t,e)),Qi}function Lf(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[Oe]=e,t[we]=a,l){case"dialog":V("cancel",t),V("close",t);break;case"iframe":case"object":case"embed":V("load",t);break;case"video":case"audio":for(l=0;l<hn.length;l++)V(hn[l],t);break;case"source":V("error",t);break;case"img":case"image":case"link":V("error",t),V("load",t);break;case"details":V("toggle",t);break;case"input":V("invalid",t),Ps(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Yn(t);break;case"select":V("invalid",t);break;case"textarea":V("invalid",t),tf(t,a.value,a.defaultValue,a.children),Yn(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||ad(t.textContent,l)?(a.popover!=null&&(V("beforetoggle",t),V("toggle",t)),a.onScroll!=null&&V("scroll",t),a.onScrollEnd!=null&&V("scrollend",t),a.onClick!=null&&(t.onclick=Cu),t=!0):t=!1,t||pl(e)}function Yf(e){for(Ce=e.return;Ce;)switch(Ce.tag){case 5:case 13:gt=!1;return;case 27:case 3:gt=!0;return;default:Ce=Ce.return}}function Ya(e){if(e!==Ce)return!1;if(!I)return Yf(e),I=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||ns(e.type,e.memoizedProps)),l=!l),l&&ye&&pl(e),Yf(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){ye=dt(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}ye=null}}else t===27?(t=ye,ul(e.type)?(e=ss,ss=null,ye=e):ye=t):ye=Ce?dt(e.stateNode.nextSibling):null;return!0}function Ga(){ye=Ce=null,I=!1}function Gf(){var e=bl;return e!==null&&(Le===null?Le=e:Le.push.apply(Le,e),bl=null),e}function Qa(e){bl===null?bl=[e]:bl.push(e)}var Xi=Ue(null),Sl=null,Dt=null;function Zt(e,t,l){te(Xi,t._currentValue),t._currentValue=l}function Ot(e){e._currentValue=Xi.current,he(Xi)}function Zi(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function ki(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var f=n.child;u=u.firstContext;e:for(;u!==null;){var o=u;u=n;for(var m=0;m<t.length;m++)if(o.context===t[m]){u.lanes|=l,o=u.alternate,o!==null&&(o.lanes|=l),Zi(u.return,l,e),a||(f=null);break e}u=o.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(s(341));f.lanes|=l,u=f.alternate,u!==null&&(u.lanes|=l),Zi(f,l,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function Xa(e,t,l,a){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var o=n.type;Qe(n.pendingProps.value,f.value)||(e!==null?e.push(o):e=[o])}}else if(n===On.current){if(f=n.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(bn):e=[bn])}n=n.return}e!==null&&ki(t,e,l,a),t.flags|=262144}function tu(e){for(e=e.firstContext;e!==null;){if(!Qe(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function jl(e){Sl=e,Dt=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Re(e){return Qf(Sl,e)}function lu(e,t){return Sl===null&&jl(e),Qf(e,t)}function Qf(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},Dt===null){if(e===null)throw Error(s(308));Dt=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Dt=Dt.next=t;return l}var sh=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},fh=i.unstable_scheduleCallback,rh=i.unstable_NormalPriority,Se={$$typeof:ie,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Vi(){return{controller:new sh,data:new Map,refCount:0}}function Za(e){e.refCount--,e.refCount===0&&fh(rh,function(){e.controller.abort()})}var ka=null,Ki=0,ta=0,la=null;function oh(e,t){if(ka===null){var l=ka=[];Ki=0,ta=Wc(),la={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Ki++,t.then(Xf,Xf),t}function Xf(){if(--Ki===0&&ka!==null){la!==null&&(la.status="fulfilled");var e=ka;ka=null,ta=0,la=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function dh(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Zf=M.S;M.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&oh(e,t),Zf!==null&&Zf(e,t)};var Tl=Ue(null);function Ji(){var e=Tl.current;return e!==null?e:ce.pooledCache}function au(e,t){t===null?te(Tl,Tl.current):te(Tl,t.pool)}function kf(){var e=Ji();return e===null?null:{parent:Se._currentValue,pool:e}}var Va=Error(s(460)),Vf=Error(s(474)),nu=Error(s(542)),Wi={then:function(){}};function Kf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function uu(){}function Jf(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(uu,uu),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,$f(e),e;default:if(typeof t.status=="string")t.then(uu,uu);else{if(e=ce,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,$f(e),e}throw Ka=t,Va}}var Ka=null;function Wf(){if(Ka===null)throw Error(s(459));var e=Ka;return Ka=null,e}function $f(e){if(e===Va||e===nu)throw Error(s(483))}var kt=!1;function $i(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Fi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Vt(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Kt(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(P&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=Fn(e),Hf(e,null,l),t}return $n(e,a,t,l),Fn(e)}function Ja(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Qs(e,l)}}function Ii(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=f:u=u.next=f,l=l.next}while(l!==null);u===null?n=u=t:u=u.next=t}else n=u=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var Pi=!1;function Wa(){if(Pi){var e=la;if(e!==null)throw e}}function $a(e,t,l,a){Pi=!1;var n=e.updateQueue;kt=!1;var u=n.firstBaseUpdate,f=n.lastBaseUpdate,o=n.shared.pending;if(o!==null){n.shared.pending=null;var m=o,b=m.next;m.next=null,f===null?u=b:f.next=b,f=m;var j=e.alternate;j!==null&&(j=j.updateQueue,o=j.lastBaseUpdate,o!==f&&(o===null?j.firstBaseUpdate=b:o.next=b,j.lastBaseUpdate=m))}if(u!==null){var N=n.baseState;f=0,j=b=m=null,o=u;do{var p=o.lane&-536870913,S=p!==o.lane;if(S?(W&p)===p:(a&p)===p){p!==0&&p===ta&&(Pi=!0),j!==null&&(j=j.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var L=e,H=o;p=t;var ne=l;switch(H.tag){case 1:if(L=H.payload,typeof L=="function"){N=L.call(ne,N,p);break e}N=L;break e;case 3:L.flags=L.flags&-65537|128;case 0:if(L=H.payload,p=typeof L=="function"?L.call(ne,N,p):L,p==null)break e;N=A({},N,p);break e;case 2:kt=!0}}p=o.callback,p!==null&&(e.flags|=64,S&&(e.flags|=8192),S=n.callbacks,S===null?n.callbacks=[p]:S.push(p))}else S={lane:p,tag:o.tag,payload:o.payload,callback:o.callback,next:null},j===null?(b=j=S,m=N):j=j.next=S,f|=p;if(o=o.next,o===null){if(o=n.shared.pending,o===null)break;S=o,o=S.next,S.next=null,n.lastBaseUpdate=S,n.shared.pending=null}}while(!0);j===null&&(m=N),n.baseState=m,n.firstBaseUpdate=b,n.lastBaseUpdate=j,u===null&&(n.shared.lanes=0),tl|=f,e.lanes=f,e.memoizedState=N}}function Ff(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function If(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)Ff(l[e],t)}var aa=Ue(null),iu=Ue(0);function Pf(e,t){e=qt,te(iu,e),te(aa,t),qt=e|t.baseLanes}function ec(){te(iu,qt),te(aa,aa.current)}function tc(){qt=iu.current,he(aa),he(iu)}var Jt=0,X=null,le=null,be=null,cu=!1,na=!1,Ml=!1,su=0,Fa=0,ua=null,hh=0;function ge(){throw Error(s(321))}function lc(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!Qe(e[l],t[l]))return!1;return!0}function ac(e,t,l,a,n,u){return Jt=u,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,M.H=e===null||e.memoizedState===null?Hr:Br,Ml=!1,u=l(a,n),Ml=!1,na&&(u=tr(t,l,a,n)),er(e),u}function er(e){M.H=mu;var t=le!==null&&le.next!==null;if(Jt=0,be=le=X=null,cu=!1,Fa=0,ua=null,t)throw Error(s(300));e===null||Ne||(e=e.dependencies,e!==null&&tu(e)&&(Ne=!0))}function tr(e,t,l,a){X=e;var n=0;do{if(na&&(ua=null),Fa=0,na=!1,25<=n)throw Error(s(301));if(n+=1,be=le=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}M.H=ph,u=t(l,a)}while(na);return u}function mh(){var e=M.H,t=e.useState()[0];return t=typeof t.then=="function"?Ia(t):t,e=e.useState()[0],(le!==null?le.memoizedState:null)!==e&&(X.flags|=1024),t}function nc(){var e=su!==0;return su=0,e}function uc(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function ic(e){if(cu){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}cu=!1}Jt=0,be=le=X=null,na=!1,Fa=su=0,ua=null}function Be(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return be===null?X.memoizedState=be=e:be=be.next=e,be}function pe(){if(le===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=le.next;var t=be===null?X.memoizedState:be.next;if(t!==null)be=t,le=e;else{if(e===null)throw X.alternate===null?Error(s(467)):Error(s(310));le=e,e={memoizedState:le.memoizedState,baseState:le.baseState,baseQueue:le.baseQueue,queue:le.queue,next:null},be===null?X.memoizedState=be=e:be=be.next=e}return be}function cc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Ia(e){var t=Fa;return Fa+=1,ua===null&&(ua=[]),e=Jf(ua,e,t),t=X,(be===null?t.memoizedState:be.next)===null&&(t=t.alternate,M.H=t===null||t.memoizedState===null?Hr:Br),e}function fu(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Ia(e);if(e.$$typeof===ie)return Re(e)}throw Error(s(438,String(e)))}function sc(e){var t=null,l=X.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=X.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=cc(),X.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=En;return t.index++,l}function Rt(e,t){return typeof t=="function"?t(e):t}function ru(e){var t=pe();return fc(t,le,e)}function fc(e,t,l){var a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=l;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var f=n.next;n.next=u.next,u.next=f}t.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var o=f=null,m=null,b=t,j=!1;do{var N=b.lane&-536870913;if(N!==b.lane?(W&N)===N:(Jt&N)===N){var p=b.revertLane;if(p===0)m!==null&&(m=m.next={lane:0,revertLane:0,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null}),N===ta&&(j=!0);else if((Jt&p)===p){b=b.next,p===ta&&(j=!0);continue}else N={lane:0,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},m===null?(o=m=N,f=u):m=m.next=N,X.lanes|=p,tl|=p;N=b.action,Ml&&l(u,N),u=b.hasEagerState?b.eagerState:l(u,N)}else p={lane:N,revertLane:b.revertLane,action:b.action,hasEagerState:b.hasEagerState,eagerState:b.eagerState,next:null},m===null?(o=m=p,f=u):m=m.next=p,X.lanes|=N,tl|=N;b=b.next}while(b!==null&&b!==t);if(m===null?f=u:m.next=o,!Qe(u,e.memoizedState)&&(Ne=!0,j&&(l=la,l!==null)))throw l;e.memoizedState=u,e.baseState=f,e.baseQueue=m,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function rc(e){var t=pe(),l=t.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,u=t.memoizedState;if(n!==null){l.pending=null;var f=n=n.next;do u=e(u,f.action),f=f.next;while(f!==n);Qe(u,t.memoizedState)||(Ne=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),l.lastRenderedState=u}return[u,a]}function lr(e,t,l){var a=X,n=pe(),u=I;if(u){if(l===void 0)throw Error(s(407));l=l()}else l=t();var f=!Qe((le||n).memoizedState,l);f&&(n.memoizedState=l,Ne=!0),n=n.queue;var o=ur.bind(null,a,n,e);if(Pa(2048,8,o,[e]),n.getSnapshot!==t||f||be!==null&&be.memoizedState.tag&1){if(a.flags|=2048,ia(9,ou(),nr.bind(null,a,n,l,t),null),ce===null)throw Error(s(349));u||(Jt&124)!==0||ar(a,t,l)}return l}function ar(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=X.updateQueue,t===null?(t=cc(),X.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function nr(e,t,l,a){t.value=l,t.getSnapshot=a,ir(t)&&cr(e)}function ur(e,t,l){return l(function(){ir(t)&&cr(e)})}function ir(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!Qe(e,l)}catch{return!0}}function cr(e){var t=Fl(e,2);t!==null&&Je(t,e,2)}function oc(e){var t=Be();if(typeof e=="function"){var l=e;if(e=l(),Ml){Gt(!0);try{l()}finally{Gt(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rt,lastRenderedState:e},t}function sr(e,t,l,a){return e.baseState=l,fc(e,le,typeof a=="function"?a:Rt)}function yh(e,t,l,a,n){if(hu(e))throw Error(s(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};M.T!==null?l(!0):u.isTransition=!1,a(u),l=t.pending,l===null?(u.next=t.pending=u,fr(t,u)):(u.next=l.next,t.pending=l.next=u)}}function fr(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var u=M.T,f={};M.T=f;try{var o=l(n,a),m=M.S;m!==null&&m(f,o),rr(e,t,o)}catch(b){dc(e,t,b)}finally{M.T=u}}else try{u=l(n,a),rr(e,t,u)}catch(b){dc(e,t,b)}}function rr(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){or(e,t,a)},function(a){return dc(e,t,a)}):or(e,t,l)}function or(e,t,l){t.status="fulfilled",t.value=l,dr(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,fr(e,l)))}function dc(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,dr(t),t=t.next;while(t!==a)}e.action=null}function dr(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function hr(e,t){return t}function mr(e,t){if(I){var l=ce.formState;if(l!==null){e:{var a=X;if(I){if(ye){t:{for(var n=ye,u=gt;n.nodeType!==8;){if(!u){n=null;break t}if(n=dt(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){ye=dt(n.nextSibling),a=n.data==="F!";break e}}pl(a)}a=!1}a&&(t=l[0])}}return l=Be(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:hr,lastRenderedState:t},l.queue=a,l=Ur.bind(null,X,a),a.dispatch=l,a=oc(!1),u=gc.bind(null,X,!1,a.queue),a=Be(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=yh.bind(null,X,n,u,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function yr(e){var t=pe();return vr(t,le,e)}function vr(e,t,l){if(t=fc(e,t,hr)[0],e=ru(Rt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Ia(t)}catch(f){throw f===Va?nu:f}else a=t;t=pe();var n=t.queue,u=n.dispatch;return l!==t.memoizedState&&(X.flags|=2048,ia(9,ou(),vh.bind(null,n,l),null)),[a,u,e]}function vh(e,t){e.action=t}function gr(e){var t=pe(),l=le;if(l!==null)return vr(t,l,e);pe(),t=t.memoizedState,l=pe();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function ia(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=X.updateQueue,t===null&&(t=cc(),X.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function ou(){return{destroy:void 0,resource:void 0}}function xr(){return pe().memoizedState}function du(e,t,l,a){var n=Be();a=a===void 0?null:a,X.flags|=e,n.memoizedState=ia(1|t,ou(),l,a)}function Pa(e,t,l,a){var n=pe();a=a===void 0?null:a;var u=n.memoizedState.inst;le!==null&&a!==null&&lc(a,le.memoizedState.deps)?n.memoizedState=ia(t,u,l,a):(X.flags|=e,n.memoizedState=ia(1|t,u,l,a))}function br(e,t){du(8390656,8,e,t)}function pr(e,t){Pa(2048,8,e,t)}function Sr(e,t){return Pa(4,2,e,t)}function jr(e,t){return Pa(4,4,e,t)}function Tr(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mr(e,t,l){l=l!=null?l.concat([e]):null,Pa(4,4,Tr.bind(null,t,e),l)}function hc(){}function Nr(e,t){var l=pe();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&lc(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function _r(e,t){var l=pe();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&lc(t,a[1]))return a[0];if(a=e(),Ml){Gt(!0);try{e()}finally{Gt(!1)}}return l.memoizedState=[a,t],a}function mc(e,t,l){return l===void 0||(Jt&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=Do(),X.lanes|=e,tl|=e,l)}function zr(e,t,l,a){return Qe(l,t)?l:aa.current!==null?(e=mc(e,l,a),Qe(e,t)||(Ne=!0),e):(Jt&42)===0?(Ne=!0,e.memoizedState=l):(e=Do(),X.lanes|=e,tl|=e,t)}function Ar(e,t,l,a,n){var u=E.p;E.p=u!==0&&8>u?u:8;var f=M.T,o={};M.T=o,gc(e,!1,t,l);try{var m=n(),b=M.S;if(b!==null&&b(o,m),m!==null&&typeof m=="object"&&typeof m.then=="function"){var j=dh(m,a);en(e,t,j,Ke(e))}else en(e,t,a,Ke(e))}catch(N){en(e,t,{then:function(){},status:"rejected",reason:N},Ke())}finally{E.p=u,M.T=f}}function gh(){}function yc(e,t,l,a){if(e.tag!==5)throw Error(s(476));var n=Er(e).queue;Ar(e,n,t,Y,l===null?gh:function(){return Dr(e),l(a)})}function Er(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Y,baseState:Y,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rt,lastRenderedState:Y},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rt,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Dr(e){var t=Er(e).next.queue;en(e,t,{},Ke())}function vc(){return Re(bn)}function Or(){return pe().memoizedState}function Rr(){return pe().memoizedState}function xh(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=Ke();e=Vt(l);var a=Kt(t,e,l);a!==null&&(Je(a,t,l),Ja(a,t,l)),t={cache:Vi()},e.payload=t;return}t=t.return}}function bh(e,t,l){var a=Ke();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},hu(e)?Cr(t,l):(l=Hi(e,t,l,a),l!==null&&(Je(l,e,a),wr(l,t,a)))}function Ur(e,t,l){var a=Ke();en(e,t,l,a)}function en(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(hu(e))Cr(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var f=t.lastRenderedState,o=u(f,l);if(n.hasEagerState=!0,n.eagerState=o,Qe(o,f))return $n(e,t,n,0),ce===null&&Wn(),!1}catch{}finally{}if(l=Hi(e,t,n,a),l!==null)return Je(l,e,a),wr(l,t,a),!0}return!1}function gc(e,t,l,a){if(a={lane:2,revertLane:Wc(),action:a,hasEagerState:!1,eagerState:null,next:null},hu(e)){if(t)throw Error(s(479))}else t=Hi(e,l,a,2),t!==null&&Je(t,e,2)}function hu(e){var t=e.alternate;return e===X||t!==null&&t===X}function Cr(e,t){na=cu=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function wr(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Qs(e,l)}}var mu={readContext:Re,use:fu,useCallback:ge,useContext:ge,useEffect:ge,useImperativeHandle:ge,useLayoutEffect:ge,useInsertionEffect:ge,useMemo:ge,useReducer:ge,useRef:ge,useState:ge,useDebugValue:ge,useDeferredValue:ge,useTransition:ge,useSyncExternalStore:ge,useId:ge,useHostTransitionStatus:ge,useFormState:ge,useActionState:ge,useOptimistic:ge,useMemoCache:ge,useCacheRefresh:ge},Hr={readContext:Re,use:fu,useCallback:function(e,t){return Be().memoizedState=[e,t===void 0?null:t],e},useContext:Re,useEffect:br,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,du(4194308,4,Tr.bind(null,t,e),l)},useLayoutEffect:function(e,t){return du(4194308,4,e,t)},useInsertionEffect:function(e,t){du(4,2,e,t)},useMemo:function(e,t){var l=Be();t=t===void 0?null:t;var a=e();if(Ml){Gt(!0);try{e()}finally{Gt(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=Be();if(l!==void 0){var n=l(t);if(Ml){Gt(!0);try{l(t)}finally{Gt(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=bh.bind(null,X,e),[a.memoizedState,e]},useRef:function(e){var t=Be();return e={current:e},t.memoizedState=e},useState:function(e){e=oc(e);var t=e.queue,l=Ur.bind(null,X,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:hc,useDeferredValue:function(e,t){var l=Be();return mc(l,e,t)},useTransition:function(){var e=oc(!1);return e=Ar.bind(null,X,e.queue,!0,!1),Be().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=X,n=Be();if(I){if(l===void 0)throw Error(s(407));l=l()}else{if(l=t(),ce===null)throw Error(s(349));(W&124)!==0||ar(a,t,l)}n.memoizedState=l;var u={value:l,getSnapshot:t};return n.queue=u,br(ur.bind(null,a,u,e),[e]),a.flags|=2048,ia(9,ou(),nr.bind(null,a,u,l,t),null),l},useId:function(){var e=Be(),t=ce.identifierPrefix;if(I){var l=Et,a=At;l=(a&~(1<<32-Ge(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=su++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=hh++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:vc,useFormState:mr,useActionState:mr,useOptimistic:function(e){var t=Be();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=gc.bind(null,X,!0,l),l.dispatch=t,[e,t]},useMemoCache:sc,useCacheRefresh:function(){return Be().memoizedState=xh.bind(null,X)}},Br={readContext:Re,use:fu,useCallback:Nr,useContext:Re,useEffect:pr,useImperativeHandle:Mr,useInsertionEffect:Sr,useLayoutEffect:jr,useMemo:_r,useReducer:ru,useRef:xr,useState:function(){return ru(Rt)},useDebugValue:hc,useDeferredValue:function(e,t){var l=pe();return zr(l,le.memoizedState,e,t)},useTransition:function(){var e=ru(Rt)[0],t=pe().memoizedState;return[typeof e=="boolean"?e:Ia(e),t]},useSyncExternalStore:lr,useId:Or,useHostTransitionStatus:vc,useFormState:yr,useActionState:yr,useOptimistic:function(e,t){var l=pe();return sr(l,le,e,t)},useMemoCache:sc,useCacheRefresh:Rr},ph={readContext:Re,use:fu,useCallback:Nr,useContext:Re,useEffect:pr,useImperativeHandle:Mr,useInsertionEffect:Sr,useLayoutEffect:jr,useMemo:_r,useReducer:rc,useRef:xr,useState:function(){return rc(Rt)},useDebugValue:hc,useDeferredValue:function(e,t){var l=pe();return le===null?mc(l,e,t):zr(l,le.memoizedState,e,t)},useTransition:function(){var e=rc(Rt)[0],t=pe().memoizedState;return[typeof e=="boolean"?e:Ia(e),t]},useSyncExternalStore:lr,useId:Or,useHostTransitionStatus:vc,useFormState:gr,useActionState:gr,useOptimistic:function(e,t){var l=pe();return le!==null?sr(l,le,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:sc,useCacheRefresh:Rr},ca=null,tn=0;function yu(e){var t=tn;return tn+=1,ca===null&&(ca=[]),Jf(ca,e,t)}function ln(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function vu(e,t){throw t.$$typeof===K?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function qr(e){var t=e._init;return t(e._payload)}function Lr(e){function t(v,y){if(e){var x=v.deletions;x===null?(v.deletions=[y],v.flags|=16):x.push(y)}}function l(v,y){if(!e)return null;for(;y!==null;)t(v,y),y=y.sibling;return null}function a(v){for(var y=new Map;v!==null;)v.key!==null?y.set(v.key,v):y.set(v.index,v),v=v.sibling;return y}function n(v,y){return v=zt(v,y),v.index=0,v.sibling=null,v}function u(v,y,x){return v.index=x,e?(x=v.alternate,x!==null?(x=x.index,x<y?(v.flags|=67108866,y):x):(v.flags|=67108866,y)):(v.flags|=1048576,y)}function f(v){return e&&v.alternate===null&&(v.flags|=67108866),v}function o(v,y,x,T){return y===null||y.tag!==6?(y=qi(x,v.mode,T),y.return=v,y):(y=n(y,x),y.return=v,y)}function m(v,y,x,T){var R=x.type;return R===oe?j(v,y,x.props.children,T,x.key):y!==null&&(y.elementType===R||typeof R=="object"&&R!==null&&R.$$typeof===ft&&qr(R)===y.type)?(y=n(y,x.props),ln(y,x),y.return=v,y):(y=In(x.type,x.key,x.props,null,v.mode,T),ln(y,x),y.return=v,y)}function b(v,y,x,T){return y===null||y.tag!==4||y.stateNode.containerInfo!==x.containerInfo||y.stateNode.implementation!==x.implementation?(y=Li(x,v.mode,T),y.return=v,y):(y=n(y,x.children||[]),y.return=v,y)}function j(v,y,x,T,R){return y===null||y.tag!==7?(y=vl(x,v.mode,T,R),y.return=v,y):(y=n(y,x),y.return=v,y)}function N(v,y,x){if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return y=qi(""+y,v.mode,x),y.return=v,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case G:return x=In(y.type,y.key,y.props,null,v.mode,x),ln(x,y),x.return=v,x;case Q:return y=Li(y,v.mode,x),y.return=v,y;case ft:var T=y._init;return y=T(y._payload),N(v,y,x)}if(Mt(y)||Ie(y))return y=vl(y,v.mode,x,null),y.return=v,y;if(typeof y.then=="function")return N(v,yu(y),x);if(y.$$typeof===ie)return N(v,lu(v,y),x);vu(v,y)}return null}function p(v,y,x,T){var R=y!==null?y.key:null;if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return R!==null?null:o(v,y,""+x,T);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case G:return x.key===R?m(v,y,x,T):null;case Q:return x.key===R?b(v,y,x,T):null;case ft:return R=x._init,x=R(x._payload),p(v,y,x,T)}if(Mt(x)||Ie(x))return R!==null?null:j(v,y,x,T,null);if(typeof x.then=="function")return p(v,y,yu(x),T);if(x.$$typeof===ie)return p(v,y,lu(v,x),T);vu(v,x)}return null}function S(v,y,x,T,R){if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return v=v.get(x)||null,o(y,v,""+T,R);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case G:return v=v.get(T.key===null?x:T.key)||null,m(y,v,T,R);case Q:return v=v.get(T.key===null?x:T.key)||null,b(y,v,T,R);case ft:var Z=T._init;return T=Z(T._payload),S(v,y,x,T,R)}if(Mt(T)||Ie(T))return v=v.get(x)||null,j(y,v,T,R,null);if(typeof T.then=="function")return S(v,y,x,yu(T),R);if(T.$$typeof===ie)return S(v,y,x,lu(y,T),R);vu(y,T)}return null}function L(v,y,x,T){for(var R=null,Z=null,U=y,B=y=0,ze=null;U!==null&&B<x.length;B++){U.index>B?(ze=U,U=null):ze=U.sibling;var F=p(v,U,x[B],T);if(F===null){U===null&&(U=ze);break}e&&U&&F.alternate===null&&t(v,U),y=u(F,y,B),Z===null?R=F:Z.sibling=F,Z=F,U=ze}if(B===x.length)return l(v,U),I&&xl(v,B),R;if(U===null){for(;B<x.length;B++)U=N(v,x[B],T),U!==null&&(y=u(U,y,B),Z===null?R=U:Z.sibling=U,Z=U);return I&&xl(v,B),R}for(U=a(U);B<x.length;B++)ze=S(U,v,B,x[B],T),ze!==null&&(e&&ze.alternate!==null&&U.delete(ze.key===null?B:ze.key),y=u(ze,y,B),Z===null?R=ze:Z.sibling=ze,Z=ze);return e&&U.forEach(function(rl){return t(v,rl)}),I&&xl(v,B),R}function H(v,y,x,T){if(x==null)throw Error(s(151));for(var R=null,Z=null,U=y,B=y=0,ze=null,F=x.next();U!==null&&!F.done;B++,F=x.next()){U.index>B?(ze=U,U=null):ze=U.sibling;var rl=p(v,U,F.value,T);if(rl===null){U===null&&(U=ze);break}e&&U&&rl.alternate===null&&t(v,U),y=u(rl,y,B),Z===null?R=rl:Z.sibling=rl,Z=rl,U=ze}if(F.done)return l(v,U),I&&xl(v,B),R;if(U===null){for(;!F.done;B++,F=x.next())F=N(v,F.value,T),F!==null&&(y=u(F,y,B),Z===null?R=F:Z.sibling=F,Z=F);return I&&xl(v,B),R}for(U=a(U);!F.done;B++,F=x.next())F=S(U,v,B,F.value,T),F!==null&&(e&&F.alternate!==null&&U.delete(F.key===null?B:F.key),y=u(F,y,B),Z===null?R=F:Z.sibling=F,Z=F);return e&&U.forEach(function(Sm){return t(v,Sm)}),I&&xl(v,B),R}function ne(v,y,x,T){if(typeof x=="object"&&x!==null&&x.type===oe&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case G:e:{for(var R=x.key;y!==null;){if(y.key===R){if(R=x.type,R===oe){if(y.tag===7){l(v,y.sibling),T=n(y,x.props.children),T.return=v,v=T;break e}}else if(y.elementType===R||typeof R=="object"&&R!==null&&R.$$typeof===ft&&qr(R)===y.type){l(v,y.sibling),T=n(y,x.props),ln(T,x),T.return=v,v=T;break e}l(v,y);break}else t(v,y);y=y.sibling}x.type===oe?(T=vl(x.props.children,v.mode,T,x.key),T.return=v,v=T):(T=In(x.type,x.key,x.props,null,v.mode,T),ln(T,x),T.return=v,v=T)}return f(v);case Q:e:{for(R=x.key;y!==null;){if(y.key===R)if(y.tag===4&&y.stateNode.containerInfo===x.containerInfo&&y.stateNode.implementation===x.implementation){l(v,y.sibling),T=n(y,x.children||[]),T.return=v,v=T;break e}else{l(v,y);break}else t(v,y);y=y.sibling}T=Li(x,v.mode,T),T.return=v,v=T}return f(v);case ft:return R=x._init,x=R(x._payload),ne(v,y,x,T)}if(Mt(x))return L(v,y,x,T);if(Ie(x)){if(R=Ie(x),typeof R!="function")throw Error(s(150));return x=R.call(x),H(v,y,x,T)}if(typeof x.then=="function")return ne(v,y,yu(x),T);if(x.$$typeof===ie)return ne(v,y,lu(v,x),T);vu(v,x)}return typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint"?(x=""+x,y!==null&&y.tag===6?(l(v,y.sibling),T=n(y,x),T.return=v,v=T):(l(v,y),T=qi(x,v.mode,T),T.return=v,v=T),f(v)):l(v,y)}return function(v,y,x,T){try{tn=0;var R=ne(v,y,x,T);return ca=null,R}catch(U){if(U===Va||U===nu)throw U;var Z=Xe(29,U,null,v.mode);return Z.lanes=T,Z.return=v,Z}finally{}}}var sa=Lr(!0),Yr=Lr(!1),ut=Ue(null),xt=null;function Wt(e){var t=e.alternate;te(je,je.current&1),te(ut,e),xt===null&&(t===null||aa.current!==null||t.memoizedState!==null)&&(xt=e)}function Gr(e){if(e.tag===22){if(te(je,je.current),te(ut,e),xt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(xt=e)}}else $t()}function $t(){te(je,je.current),te(ut,ut.current)}function Ut(e){he(ut),xt===e&&(xt=null),he(je)}var je=Ue(0);function gu(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||cs(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function xc(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:A({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var bc={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=Ke(),n=Vt(a);n.payload=t,l!=null&&(n.callback=l),t=Kt(e,n,a),t!==null&&(Je(t,e,a),Ja(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=Ke(),n=Vt(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=Kt(e,n,a),t!==null&&(Je(t,e,a),Ja(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=Ke(),a=Vt(l);a.tag=2,t!=null&&(a.callback=t),t=Kt(e,a,l),t!==null&&(Je(t,e,l),Ja(t,e,l))}};function Qr(e,t,l,a,n,u,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,f):t.prototype&&t.prototype.isPureReactComponent?!qa(l,a)||!qa(n,u):!0}function Xr(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&bc.enqueueReplaceState(t,t.state,null)}function Nl(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=A({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var xu=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Zr(e){xu(e)}function kr(e){console.error(e)}function Vr(e){xu(e)}function bu(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Kr(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function pc(e,t,l){return l=Vt(l),l.tag=3,l.payload={element:null},l.callback=function(){bu(e,t)},l}function Jr(e){return e=Vt(e),e.tag=3,e}function Wr(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){Kr(t,l,a)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Kr(t,l,a),typeof n!="function"&&(ll===null?ll=new Set([this]):ll.add(this));var o=a.stack;this.componentDidCatch(a.value,{componentStack:o!==null?o:""})})}function Sh(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&Xa(t,l,n,!0),l=ut.current,l!==null){switch(l.tag){case 13:return xt===null?Zc():l.alternate===null&&ve===0&&(ve=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Wi?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),Vc(e,a,n)),!1;case 22:return l.flags|=65536,a===Wi?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),Vc(e,a,n)),!1}throw Error(s(435,l.tag))}return Vc(e,a,n),Zc(),!1}if(I)return t=ut.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==Qi&&(e=Error(s(422),{cause:a}),Qa(tt(e,l)))):(a!==Qi&&(t=Error(s(423),{cause:a}),Qa(tt(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=tt(a,l),n=pc(e.stateNode,a,n),Ii(e,n),ve!==4&&(ve=2)),!1;var u=Error(s(520),{cause:a});if(u=tt(u,l),rn===null?rn=[u]:rn.push(u),ve!==4&&(ve=2),t===null)return!0;a=tt(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=pc(l.stateNode,a,e),Ii(l,e),!1;case 1:if(t=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(ll===null||!ll.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=Jr(n),Wr(n,e,l,a),Ii(l,n),!1}l=l.return}while(l!==null);return!1}var $r=Error(s(461)),Ne=!1;function Ae(e,t,l,a){t.child=e===null?Yr(t,null,l,a):sa(t,e.child,l,a)}function Fr(e,t,l,a,n){l=l.render;var u=t.ref;if("ref"in a){var f={};for(var o in a)o!=="ref"&&(f[o]=a[o])}else f=a;return jl(t),a=ac(e,t,l,f,u,n),o=nc(),e!==null&&!Ne?(uc(e,t,n),Ct(e,t,n)):(I&&o&&Yi(t),t.flags|=1,Ae(e,t,a,n),t.child)}function Ir(e,t,l,a,n){if(e===null){var u=l.type;return typeof u=="function"&&!Bi(u)&&u.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=u,Pr(e,t,u,a,n)):(e=In(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!Ac(e,n)){var f=u.memoizedProps;if(l=l.compare,l=l!==null?l:qa,l(f,a)&&e.ref===t.ref)return Ct(e,t,n)}return t.flags|=1,e=zt(u,a),e.ref=t.ref,e.return=t,t.child=e}function Pr(e,t,l,a,n){if(e!==null){var u=e.memoizedProps;if(qa(u,a)&&e.ref===t.ref)if(Ne=!1,t.pendingProps=a=u,Ac(e,n))(e.flags&131072)!==0&&(Ne=!0);else return t.lanes=e.lanes,Ct(e,t,n)}return Sc(e,t,l,a,n)}function eo(e,t,l){var a=t.pendingProps,n=a.children,u=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return to(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&au(t,u!==null?u.cachePool:null),u!==null?Pf(t,u):ec(),Gr(t);else return t.lanes=t.childLanes=536870912,to(e,t,u!==null?u.baseLanes|l:l,l)}else u!==null?(au(t,u.cachePool),Pf(t,u),$t(),t.memoizedState=null):(e!==null&&au(t,null),ec(),$t());return Ae(e,t,n,l),t.child}function to(e,t,l,a){var n=Ji();return n=n===null?null:{parent:Se._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&au(t,null),ec(),Gr(t),e!==null&&Xa(e,t,a,!0),null}function pu(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function Sc(e,t,l,a,n){return jl(t),l=ac(e,t,l,a,void 0,n),a=nc(),e!==null&&!Ne?(uc(e,t,n),Ct(e,t,n)):(I&&a&&Yi(t),t.flags|=1,Ae(e,t,l,n),t.child)}function lo(e,t,l,a,n,u){return jl(t),t.updateQueue=null,l=tr(t,a,l,n),er(e),a=nc(),e!==null&&!Ne?(uc(e,t,u),Ct(e,t,u)):(I&&a&&Yi(t),t.flags|=1,Ae(e,t,l,u),t.child)}function ao(e,t,l,a,n){if(jl(t),t.stateNode===null){var u=Il,f=l.contextType;typeof f=="object"&&f!==null&&(u=Re(f)),u=new l(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=bc,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},$i(t),f=l.contextType,u.context=typeof f=="object"&&f!==null?Re(f):Il,u.state=t.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(xc(t,l,f,a),u.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&bc.enqueueReplaceState(u,u.state,null),$a(t,a,u,n),Wa(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var o=t.memoizedProps,m=Nl(l,o);u.props=m;var b=u.context,j=l.contextType;f=Il,typeof j=="object"&&j!==null&&(f=Re(j));var N=l.getDerivedStateFromProps;j=typeof N=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=t.pendingProps!==o,j||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||b!==f)&&Xr(t,u,a,f),kt=!1;var p=t.memoizedState;u.state=p,$a(t,a,u,n),Wa(),b=t.memoizedState,o||p!==b||kt?(typeof N=="function"&&(xc(t,l,N,a),b=t.memoizedState),(m=kt||Qr(t,l,m,a,p,b,f))?(j||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=b),u.props=a,u.state=b,u.context=f,a=m):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,Fi(e,t),f=t.memoizedProps,j=Nl(l,f),u.props=j,N=t.pendingProps,p=u.context,b=l.contextType,m=Il,typeof b=="object"&&b!==null&&(m=Re(b)),o=l.getDerivedStateFromProps,(b=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==N||p!==m)&&Xr(t,u,a,m),kt=!1,p=t.memoizedState,u.state=p,$a(t,a,u,n),Wa();var S=t.memoizedState;f!==N||p!==S||kt||e!==null&&e.dependencies!==null&&tu(e.dependencies)?(typeof o=="function"&&(xc(t,l,o,a),S=t.memoizedState),(j=kt||Qr(t,l,j,a,p,S,m)||e!==null&&e.dependencies!==null&&tu(e.dependencies))?(b||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,S,m),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,S,m)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=S),u.props=a,u.state=S,u.context=m,a=j):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,pu(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=sa(t,e.child,null,n),t.child=sa(t,null,l,n)):Ae(e,t,l,n),t.memoizedState=u.state,e=t.child):e=Ct(e,t,n),e}function no(e,t,l,a){return Ga(),t.flags|=256,Ae(e,t,l,a),t.child}var jc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Tc(e){return{baseLanes:e,cachePool:kf()}}function Mc(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=it),e}function uo(e,t,l){var a=t.pendingProps,n=!1,u=(t.flags&128)!==0,f;if((f=u)||(f=e!==null&&e.memoizedState===null?!1:(je.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(I){if(n?Wt(t):$t(),I){var o=ye,m;if(m=o){e:{for(m=o,o=gt;m.nodeType!==8;){if(!o){o=null;break e}if(m=dt(m.nextSibling),m===null){o=null;break e}}o=m}o!==null?(t.memoizedState={dehydrated:o,treeContext:gl!==null?{id:At,overflow:Et}:null,retryLane:536870912,hydrationErrors:null},m=Xe(18,null,null,0),m.stateNode=o,m.return=t,t.child=m,Ce=t,ye=null,m=!0):m=!1}m||pl(t)}if(o=t.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return cs(o)?t.lanes=32:t.lanes=536870912,null;Ut(t)}return o=a.children,a=a.fallback,n?($t(),n=t.mode,o=Su({mode:"hidden",children:o},n),a=vl(a,n,l,null),o.return=t,a.return=t,o.sibling=a,t.child=o,n=t.child,n.memoizedState=Tc(l),n.childLanes=Mc(e,f,l),t.memoizedState=jc,a):(Wt(t),Nc(t,o))}if(m=e.memoizedState,m!==null&&(o=m.dehydrated,o!==null)){if(u)t.flags&256?(Wt(t),t.flags&=-257,t=_c(e,t,l)):t.memoizedState!==null?($t(),t.child=e.child,t.flags|=128,t=null):($t(),n=a.fallback,o=t.mode,a=Su({mode:"visible",children:a.children},o),n=vl(n,o,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,sa(t,e.child,null,l),a=t.child,a.memoizedState=Tc(l),a.childLanes=Mc(e,f,l),t.memoizedState=jc,t=n);else if(Wt(t),cs(o)){if(f=o.nextSibling&&o.nextSibling.dataset,f)var b=f.dgst;f=b,a=Error(s(419)),a.stack="",a.digest=f,Qa({value:a,source:null,stack:null}),t=_c(e,t,l)}else if(Ne||Xa(e,t,l,!1),f=(l&e.childLanes)!==0,Ne||f){if(f=ce,f!==null&&(a=l&-l,a=(a&42)!==0?1:si(a),a=(a&(f.suspendedLanes|l))!==0?0:a,a!==0&&a!==m.retryLane))throw m.retryLane=a,Fl(e,a),Je(f,e,a),$r;o.data==="$?"||Zc(),t=_c(e,t,l)}else o.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=m.treeContext,ye=dt(o.nextSibling),Ce=t,I=!0,bl=null,gt=!1,e!==null&&(at[nt++]=At,at[nt++]=Et,at[nt++]=gl,At=e.id,Et=e.overflow,gl=t),t=Nc(t,a.children),t.flags|=4096);return t}return n?($t(),n=a.fallback,o=t.mode,m=e.child,b=m.sibling,a=zt(m,{mode:"hidden",children:a.children}),a.subtreeFlags=m.subtreeFlags&65011712,b!==null?n=zt(b,n):(n=vl(n,o,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,o=e.child.memoizedState,o===null?o=Tc(l):(m=o.cachePool,m!==null?(b=Se._currentValue,m=m.parent!==b?{parent:b,pool:b}:m):m=kf(),o={baseLanes:o.baseLanes|l,cachePool:m}),n.memoizedState=o,n.childLanes=Mc(e,f,l),t.memoizedState=jc,a):(Wt(t),l=e.child,e=l.sibling,l=zt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=l,t.memoizedState=null,l)}function Nc(e,t){return t=Su({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Su(e,t){return e=Xe(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function _c(e,t,l){return sa(t,e.child,null,l),e=Nc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function io(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Zi(e.return,t,l)}function zc(e,t,l,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function co(e,t,l){var a=t.pendingProps,n=a.revealOrder,u=a.tail;if(Ae(e,t,a.children,l),a=je.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&io(e,l,t);else if(e.tag===19)io(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(te(je,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&gu(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),zc(t,!1,n,l,u);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&gu(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}zc(t,!0,l,null,u);break;case"together":zc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ct(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),tl|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Xa(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,l=zt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=zt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function Ac(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&tu(e)))}function jh(e,t,l){switch(t.tag){case 3:Rn(t,t.stateNode.containerInfo),Zt(t,Se,e.memoizedState.cache),Ga();break;case 27:case 5:ai(t);break;case 4:Rn(t,t.stateNode.containerInfo);break;case 10:Zt(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(Wt(t),t.flags|=128,null):(l&t.child.childLanes)!==0?uo(e,t,l):(Wt(t),e=Ct(e,t,l),e!==null?e.sibling:null);Wt(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(Xa(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return co(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),te(je,je.current),a)break;return null;case 22:case 23:return t.lanes=0,eo(e,t,l);case 24:Zt(t,Se,e.memoizedState.cache)}return Ct(e,t,l)}function so(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ne=!0;else{if(!Ac(e,l)&&(t.flags&128)===0)return Ne=!1,jh(e,t,l);Ne=(e.flags&131072)!==0}else Ne=!1,I&&(t.flags&1048576)!==0&&qf(t,eu,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")Bi(a)?(e=Nl(a,e),t.tag=1,t=ao(null,t,a,e,l)):(t.tag=0,t=Sc(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===Fe){t.tag=11,t=Fr(null,t,a,e,l);break e}else if(n===Tt){t.tag=14,t=Ir(null,t,a,e,l);break e}}throw t=Na(a)||a,Error(s(306,t,""))}}return t;case 0:return Sc(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=Nl(a,t.pendingProps),ao(e,t,a,n,l);case 3:e:{if(Rn(t,t.stateNode.containerInfo),e===null)throw Error(s(387));a=t.pendingProps;var u=t.memoizedState;n=u.element,Fi(e,t),$a(t,a,null,l);var f=t.memoizedState;if(a=f.cache,Zt(t,Se,a),a!==u.cache&&ki(t,[Se],l,!0),Wa(),a=f.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=no(e,t,a,l);break e}else if(a!==n){n=tt(Error(s(424)),t),Qa(n),t=no(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ye=dt(e.firstChild),Ce=t,I=!0,bl=null,gt=!0,l=Yr(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Ga(),a===n){t=Ct(e,t,l);break e}Ae(e,t,a,l)}t=t.child}return t;case 26:return pu(e,t),e===null?(l=hd(t.type,null,t.pendingProps,null))?t.memoizedState=l:I||(l=t.type,e=t.pendingProps,a=wu(Yt.current).createElement(l),a[Oe]=t,a[we]=e,De(a,l,e),Me(a),t.stateNode=a):t.memoizedState=hd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ai(t),e===null&&I&&(a=t.stateNode=rd(t.type,t.pendingProps,Yt.current),Ce=t,gt=!0,n=ye,ul(t.type)?(ss=n,ye=dt(a.firstChild)):ye=n),Ae(e,t,t.pendingProps.children,l),pu(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&I&&((n=a=ye)&&(a=$h(a,t.type,t.pendingProps,gt),a!==null?(t.stateNode=a,Ce=t,ye=dt(a.firstChild),gt=!1,n=!0):n=!1),n||pl(t)),ai(t),n=t.type,u=t.pendingProps,f=e!==null?e.memoizedProps:null,a=u.children,ns(n,u)?a=null:f!==null&&ns(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=ac(e,t,mh,null,null,l),bn._currentValue=n),pu(e,t),Ae(e,t,a,l),t.child;case 6:return e===null&&I&&((e=l=ye)&&(l=Fh(l,t.pendingProps,gt),l!==null?(t.stateNode=l,Ce=t,ye=null,e=!0):e=!1),e||pl(t)),null;case 13:return uo(e,t,l);case 4:return Rn(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=sa(t,null,a,l):Ae(e,t,a,l),t.child;case 11:return Fr(e,t,t.type,t.pendingProps,l);case 7:return Ae(e,t,t.pendingProps,l),t.child;case 8:return Ae(e,t,t.pendingProps.children,l),t.child;case 12:return Ae(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,Zt(t,t.type,a.value),Ae(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,jl(t),n=Re(n),a=a(n),t.flags|=1,Ae(e,t,a,l),t.child;case 14:return Ir(e,t,t.type,t.pendingProps,l);case 15:return Pr(e,t,t.type,t.pendingProps,l);case 19:return co(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Su(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=zt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return eo(e,t,l);case 24:return jl(t),a=Re(Se),e===null?(n=Ji(),n===null&&(n=ce,u=Vi(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),t.memoizedState={parent:a,cache:n},$i(t),Zt(t,Se,n)):((e.lanes&l)!==0&&(Fi(e,t),$a(t,null,null,l),Wa()),n=e.memoizedState,u=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Zt(t,Se,a)):(a=u.cache,Zt(t,Se,a),a!==n.cache&&ki(t,[Se],l,!0))),Ae(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function wt(e){e.flags|=4}function fo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!xd(t)){if(t=ut.current,t!==null&&((W&4194048)===W?xt!==null:(W&62914560)!==W&&(W&536870912)===0||t!==xt))throw Ka=Wi,Vf;e.flags|=8192}}function ju(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Ys():536870912,e.lanes|=t,da|=t)}function an(e,t){if(!I)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function me(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function Th(e,t,l){var a=t.pendingProps;switch(Gi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return me(t),null;case 1:return me(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Ot(Se),Hl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Ya(t)?wt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Gf())),me(t),null;case 26:return l=t.memoizedState,e===null?(wt(t),l!==null?(me(t),fo(t,l)):(me(t),t.flags&=-16777217)):l?l!==e.memoizedState?(wt(t),me(t),fo(t,l)):(me(t),t.flags&=-16777217):(e.memoizedProps!==a&&wt(t),me(t),t.flags&=-16777217),null;case 27:Un(t),l=Yt.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&wt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return me(t),null}e=Te.current,Ya(t)?Lf(t):(e=rd(n,a,l),t.stateNode=e,wt(t))}return me(t),null;case 5:if(Un(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&wt(t);else{if(!a){if(t.stateNode===null)throw Error(s(166));return me(t),null}if(e=Te.current,Ya(t))Lf(t);else{switch(n=wu(Yt.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[Oe]=t,e[we]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(De(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&wt(t)}}return me(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&wt(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(s(166));if(e=Yt.current,Ya(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=Ce,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[Oe]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||ad(e.nodeValue,l)),e||pl(t)}else e=wu(e).createTextNode(a),e[Oe]=t,t.stateNode=e}return me(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Ya(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(s(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[Oe]=t}else Ga(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;me(t),n=!1}else n=Gf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Ut(t),t):(Ut(t),null)}if(Ut(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),ju(t,t.updateQueue),me(t),null;case 4:return Hl(),e===null&&Pc(t.stateNode.containerInfo),me(t),null;case 10:return Ot(t.type),me(t),null;case 19:if(he(je),n=t.memoizedState,n===null)return me(t),null;if(a=(t.flags&128)!==0,u=n.rendering,u===null)if(a)an(n,!1);else{if(ve!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=gu(e),u!==null){for(t.flags|=128,an(n,!1),e=u.updateQueue,t.updateQueue=e,ju(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Bf(l,e),l=l.sibling;return te(je,je.current&1|2),t.child}e=e.sibling}n.tail!==null&&vt()>Nu&&(t.flags|=128,a=!0,an(n,!1),t.lanes=4194304)}else{if(!a)if(e=gu(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,ju(t,e),an(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!I)return me(t),null}else 2*vt()-n.renderingStartTime>Nu&&l!==536870912&&(t.flags|=128,a=!0,an(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=vt(),t.sibling=null,e=je.current,te(je,a?e&1|2:e&1),t):(me(t),null);case 22:case 23:return Ut(t),tc(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(me(t),t.subtreeFlags&6&&(t.flags|=8192)):me(t),l=t.updateQueue,l!==null&&ju(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&he(Tl),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Ot(Se),me(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function Mh(e,t){switch(Gi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ot(Se),Hl(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Un(t),null;case 13:if(Ut(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Ga()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return he(je),null;case 4:return Hl(),null;case 10:return Ot(t.type),null;case 22:case 23:return Ut(t),tc(),e!==null&&he(Tl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Ot(Se),null;case 25:return null;default:return null}}function ro(e,t){switch(Gi(t),t.tag){case 3:Ot(Se),Hl();break;case 26:case 27:case 5:Un(t);break;case 4:Hl();break;case 13:Ut(t);break;case 19:he(je);break;case 10:Ot(t.type);break;case 22:case 23:Ut(t),tc(),e!==null&&he(Tl);break;case 24:Ot(Se)}}function nn(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var u=l.create,f=l.inst;a=u(),f.destroy=a}l=l.next}while(l!==n)}}catch(o){ue(t,t.return,o)}}function Ft(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var f=a.inst,o=f.destroy;if(o!==void 0){f.destroy=void 0,n=t;var m=l,b=o;try{b()}catch(j){ue(n,m,j)}}}a=a.next}while(a!==u)}}catch(j){ue(t,t.return,j)}}function oo(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{If(t,l)}catch(a){ue(e,e.return,a)}}}function ho(e,t,l){l.props=Nl(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){ue(e,t,a)}}function un(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){ue(e,t,n)}}function bt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){ue(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){ue(e,t,n)}else l.current=null}function mo(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){ue(e,e.return,n)}}function Ec(e,t,l){try{var a=e.stateNode;kh(a,e.type,l,t),a[we]=t}catch(n){ue(e,e.return,n)}}function yo(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ul(e.type)||e.tag===4}function Dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||yo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ul(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Oc(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=Cu));else if(a!==4&&(a===27&&ul(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(Oc(e,t,l),e=e.sibling;e!==null;)Oc(e,t,l),e=e.sibling}function Tu(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&ul(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(Tu(e,t,l),e=e.sibling;e!==null;)Tu(e,t,l),e=e.sibling}function vo(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);De(t,a,l),t[Oe]=e,t[we]=l}catch(u){ue(e,e.return,u)}}var Ht=!1,xe=!1,Rc=!1,go=typeof WeakSet=="function"?WeakSet:Set,_e=null;function Nh(e,t){if(e=e.containerInfo,ls=Gu,e=zf(e),Di(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break e}var f=0,o=-1,m=-1,b=0,j=0,N=e,p=null;t:for(;;){for(var S;N!==l||n!==0&&N.nodeType!==3||(o=f+n),N!==u||a!==0&&N.nodeType!==3||(m=f+a),N.nodeType===3&&(f+=N.nodeValue.length),(S=N.firstChild)!==null;)p=N,N=S;for(;;){if(N===e)break t;if(p===l&&++b===n&&(o=f),p===u&&++j===a&&(m=f),(S=N.nextSibling)!==null)break;N=p,p=N.parentNode}N=S}l=o===-1||m===-1?null:{start:o,end:m}}else l=null}l=l||{start:0,end:0}}else l=null;for(as={focusedElem:e,selectionRange:l},Gu=!1,_e=t;_e!==null;)if(t=_e,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,_e=e;else for(;_e!==null;){switch(t=_e,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,l=t,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var L=Nl(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(L,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(H){ue(l,l.return,H)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)is(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":is(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,_e=e;break}_e=t.return}}function xo(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:It(e,l),a&4&&nn(5,l);break;case 1:if(It(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(f){ue(l,l.return,f)}else{var n=Nl(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ue(l,l.return,f)}}a&64&&oo(l),a&512&&un(l,l.return);break;case 3:if(It(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{If(e,t)}catch(f){ue(l,l.return,f)}}break;case 27:t===null&&a&4&&vo(l);case 26:case 5:It(e,l),t===null&&a&4&&mo(l),a&512&&un(l,l.return);break;case 12:It(e,l);break;case 13:It(e,l),a&4&&So(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=Ch.bind(null,l),Ih(e,l))));break;case 22:if(a=l.memoizedState!==null||Ht,!a){t=t!==null&&t.memoizedState!==null||xe,n=Ht;var u=xe;Ht=a,(xe=t)&&!u?Pt(e,l,(l.subtreeFlags&8772)!==0):It(e,l),Ht=n,xe=u}break;case 30:break;default:It(e,l)}}function bo(e){var t=e.alternate;t!==null&&(e.alternate=null,bo(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&oi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var de=null,qe=!1;function Bt(e,t,l){for(l=l.child;l!==null;)po(e,t,l),l=l.sibling}function po(e,t,l){if(Ye&&typeof Ye.onCommitFiberUnmount=="function")try{Ye.onCommitFiberUnmount(_a,l)}catch{}switch(l.tag){case 26:xe||bt(l,t),Bt(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:xe||bt(l,t);var a=de,n=qe;ul(l.type)&&(de=l.stateNode,qe=!1),Bt(e,t,l),yn(l.stateNode),de=a,qe=n;break;case 5:xe||bt(l,t);case 6:if(a=de,n=qe,de=null,Bt(e,t,l),de=a,qe=n,de!==null)if(qe)try{(de.nodeType===9?de.body:de.nodeName==="HTML"?de.ownerDocument.body:de).removeChild(l.stateNode)}catch(u){ue(l,t,u)}else try{de.removeChild(l.stateNode)}catch(u){ue(l,t,u)}break;case 18:de!==null&&(qe?(e=de,sd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),Tn(e)):sd(de,l.stateNode));break;case 4:a=de,n=qe,de=l.stateNode.containerInfo,qe=!0,Bt(e,t,l),de=a,qe=n;break;case 0:case 11:case 14:case 15:xe||Ft(2,l,t),xe||Ft(4,l,t),Bt(e,t,l);break;case 1:xe||(bt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&ho(l,t,a)),Bt(e,t,l);break;case 21:Bt(e,t,l);break;case 22:xe=(a=xe)||l.memoizedState!==null,Bt(e,t,l),xe=a;break;default:Bt(e,t,l)}}function So(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Tn(e)}catch(l){ue(t,t.return,l)}}function _h(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new go),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new go),t;default:throw Error(s(435,e.tag))}}function Uc(e,t){var l=_h(e);t.forEach(function(a){var n=wh.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function Ze(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=e,f=t,o=f;e:for(;o!==null;){switch(o.tag){case 27:if(ul(o.type)){de=o.stateNode,qe=!1;break e}break;case 5:de=o.stateNode,qe=!1;break e;case 3:case 4:de=o.stateNode.containerInfo,qe=!0;break e}o=o.return}if(de===null)throw Error(s(160));po(u,f,n),de=null,qe=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)jo(t,e),t=t.sibling}var ot=null;function jo(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ze(t,e),ke(e),a&4&&(Ft(3,e,e.return),nn(3,e),Ft(5,e,e.return));break;case 1:Ze(t,e),ke(e),a&512&&(xe||l===null||bt(l,l.return)),a&64&&Ht&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=ot;if(Ze(t,e),ke(e),a&512&&(xe||l===null||bt(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[Ea]||u[Oe]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),De(u,a,l),u[Oe]=e,Me(u),a=u;break e;case"link":var f=vd("link","href",n).get(a+(l.href||""));if(f){for(var o=0;o<f.length;o++)if(u=f[o],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(o,1);break t}}u=n.createElement(a),De(u,a,l),n.head.appendChild(u);break;case"meta":if(f=vd("meta","content",n).get(a+(l.content||""))){for(o=0;o<f.length;o++)if(u=f[o],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(o,1);break t}}u=n.createElement(a),De(u,a,l),n.head.appendChild(u);break;default:throw Error(s(468,a))}u[Oe]=e,Me(u),a=u}e.stateNode=a}else gd(n,e.type,e.stateNode);else e.stateNode=yd(n,a,e.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?gd(n,e.type,e.stateNode):yd(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Ec(e,e.memoizedProps,l.memoizedProps)}break;case 27:Ze(t,e),ke(e),a&512&&(xe||l===null||bt(l,l.return)),l!==null&&a&4&&Ec(e,e.memoizedProps,l.memoizedProps);break;case 5:if(Ze(t,e),ke(e),a&512&&(xe||l===null||bt(l,l.return)),e.flags&32){n=e.stateNode;try{Zl(n,"")}catch(S){ue(e,e.return,S)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,Ec(e,n,l!==null?l.memoizedProps:n)),a&1024&&(Rc=!0);break;case 6:if(Ze(t,e),ke(e),a&4){if(e.stateNode===null)throw Error(s(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(S){ue(e,e.return,S)}}break;case 3:if(qu=null,n=ot,ot=Hu(t.containerInfo),Ze(t,e),ot=n,ke(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{Tn(t.containerInfo)}catch(S){ue(e,e.return,S)}Rc&&(Rc=!1,To(e));break;case 4:a=ot,ot=Hu(e.stateNode.containerInfo),Ze(t,e),ke(e),ot=a;break;case 12:Ze(t,e),ke(e);break;case 13:Ze(t,e),ke(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Lc=vt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Uc(e,a)));break;case 22:n=e.memoizedState!==null;var m=l!==null&&l.memoizedState!==null,b=Ht,j=xe;if(Ht=b||n,xe=j||m,Ze(t,e),xe=j,Ht=b,ke(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||m||Ht||xe||_l(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){m=l=t;try{if(u=m.stateNode,n)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{o=m.stateNode;var N=m.memoizedProps.style,p=N!=null&&N.hasOwnProperty("display")?N.display:null;o.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(S){ue(m,m.return,S)}}}else if(t.tag===6){if(l===null){m=t;try{m.stateNode.nodeValue=n?"":m.memoizedProps}catch(S){ue(m,m.return,S)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Uc(e,l))));break;case 19:Ze(t,e),ke(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Uc(e,a)));break;case 30:break;case 21:break;default:Ze(t,e),ke(e)}}function ke(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(yo(a)){l=a;break}a=a.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var n=l.stateNode,u=Dc(e);Tu(e,u,n);break;case 5:var f=l.stateNode;l.flags&32&&(Zl(f,""),l.flags&=-33);var o=Dc(e);Tu(e,o,f);break;case 3:case 4:var m=l.stateNode.containerInfo,b=Dc(e);Oc(e,b,m);break;default:throw Error(s(161))}}catch(j){ue(e,e.return,j)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function To(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;To(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function It(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)xo(e,t.alternate,t),t=t.sibling}function _l(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ft(4,t,t.return),_l(t);break;case 1:bt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&ho(t,t.return,l),_l(t);break;case 27:yn(t.stateNode);case 26:case 5:bt(t,t.return),_l(t);break;case 22:t.memoizedState===null&&_l(t);break;case 30:_l(t);break;default:_l(t)}e=e.sibling}}function Pt(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,u=t,f=u.flags;switch(u.tag){case 0:case 11:case 15:Pt(n,u,l),nn(4,u);break;case 1:if(Pt(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(b){ue(a,a.return,b)}if(a=u,n=a.updateQueue,n!==null){var o=a.stateNode;try{var m=n.shared.hiddenCallbacks;if(m!==null)for(n.shared.hiddenCallbacks=null,n=0;n<m.length;n++)Ff(m[n],o)}catch(b){ue(a,a.return,b)}}l&&f&64&&oo(u),un(u,u.return);break;case 27:vo(u);case 26:case 5:Pt(n,u,l),l&&a===null&&f&4&&mo(u),un(u,u.return);break;case 12:Pt(n,u,l);break;case 13:Pt(n,u,l),l&&f&4&&So(n,u);break;case 22:u.memoizedState===null&&Pt(n,u,l),un(u,u.return);break;case 30:break;default:Pt(n,u,l)}t=t.sibling}}function Cc(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&Za(l))}function wc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Za(e))}function pt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Mo(e,t,l,a),t=t.sibling}function Mo(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:pt(e,t,l,a),n&2048&&nn(9,t);break;case 1:pt(e,t,l,a);break;case 3:pt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Za(e)));break;case 12:if(n&2048){pt(e,t,l,a),e=t.stateNode;try{var u=t.memoizedProps,f=u.id,o=u.onPostCommit;typeof o=="function"&&o(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(m){ue(t,t.return,m)}}else pt(e,t,l,a);break;case 13:pt(e,t,l,a);break;case 23:break;case 22:u=t.stateNode,f=t.alternate,t.memoizedState!==null?u._visibility&2?pt(e,t,l,a):cn(e,t):u._visibility&2?pt(e,t,l,a):(u._visibility|=2,fa(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&Cc(f,t);break;case 24:pt(e,t,l,a),n&2048&&wc(t.alternate,t);break;default:pt(e,t,l,a)}}function fa(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,f=t,o=l,m=a,b=f.flags;switch(f.tag){case 0:case 11:case 15:fa(u,f,o,m,n),nn(8,f);break;case 23:break;case 22:var j=f.stateNode;f.memoizedState!==null?j._visibility&2?fa(u,f,o,m,n):cn(u,f):(j._visibility|=2,fa(u,f,o,m,n)),n&&b&2048&&Cc(f.alternate,f);break;case 24:fa(u,f,o,m,n),n&&b&2048&&wc(f.alternate,f);break;default:fa(u,f,o,m,n)}t=t.sibling}}function cn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:cn(l,a),n&2048&&Cc(a.alternate,a);break;case 24:cn(l,a),n&2048&&wc(a.alternate,a);break;default:cn(l,a)}t=t.sibling}}var sn=8192;function ra(e){if(e.subtreeFlags&sn)for(e=e.child;e!==null;)No(e),e=e.sibling}function No(e){switch(e.tag){case 26:ra(e),e.flags&sn&&e.memoizedState!==null&&om(ot,e.memoizedState,e.memoizedProps);break;case 5:ra(e);break;case 3:case 4:var t=ot;ot=Hu(e.stateNode.containerInfo),ra(e),ot=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=sn,sn=16777216,ra(e),sn=t):ra(e));break;default:ra(e)}}function _o(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function fn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];_e=a,Ao(a,e)}_o(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)zo(e),e=e.sibling}function zo(e){switch(e.tag){case 0:case 11:case 15:fn(e),e.flags&2048&&Ft(9,e,e.return);break;case 3:fn(e);break;case 12:fn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Mu(e)):fn(e);break;default:fn(e)}}function Mu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];_e=a,Ao(a,e)}_o(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ft(8,t,t.return),Mu(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,Mu(t));break;default:Mu(t)}e=e.sibling}}function Ao(e,t){for(;_e!==null;){var l=_e;switch(l.tag){case 0:case 11:case 15:Ft(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Za(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,_e=a;else e:for(l=e;_e!==null;){a=_e;var n=a.sibling,u=a.return;if(bo(a),a===l){_e=null;break e}if(n!==null){n.return=u,_e=n;break e}_e=u}}}var zh={getCacheForType:function(e){var t=Re(Se),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},Ah=typeof WeakMap=="function"?WeakMap:Map,P=0,ce=null,k=null,W=0,ee=0,Ve=null,el=!1,oa=!1,Hc=!1,qt=0,ve=0,tl=0,zl=0,Bc=0,it=0,da=0,rn=null,Le=null,qc=!1,Lc=0,Nu=1/0,_u=null,ll=null,Ee=0,al=null,ha=null,ma=0,Yc=0,Gc=null,Eo=null,on=0,Qc=null;function Ke(){if((P&2)!==0&&W!==0)return W&-W;if(M.T!==null){var e=ta;return e!==0?e:Wc()}return Xs()}function Do(){it===0&&(it=(W&536870912)===0||I?Ls():536870912);var e=ut.current;return e!==null&&(e.flags|=32),it}function Je(e,t,l){(e===ce&&(ee===2||ee===9)||e.cancelPendingCommit!==null)&&(ya(e,0),nl(e,W,it,!1)),Aa(e,l),((P&2)===0||e!==ce)&&(e===ce&&((P&2)===0&&(zl|=l),ve===4&&nl(e,W,it,!1)),St(e))}function Oo(e,t,l){if((P&6)!==0)throw Error(s(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||za(e,t),n=a?Oh(e,t):kc(e,t,!0),u=a;do{if(n===0){oa&&!a&&nl(e,t,0,!1);break}else{if(l=e.current.alternate,u&&!Eh(l)){n=kc(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var o=e;n=rn;var m=o.current.memoizedState.isDehydrated;if(m&&(ya(o,f).flags|=256),f=kc(o,f,!1),f!==2){if(Hc&&!m){o.errorRecoveryDisabledLanes|=u,zl|=u,n=4;break e}u=Le,Le=n,u!==null&&(Le===null?Le=u:Le.push.apply(Le,u))}n=f}if(u=!1,n!==2)continue}}if(n===1){ya(e,0),nl(e,t,0,!0);break}e:{switch(a=e,u=n,u){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:nl(a,t,it,!el);break e;case 2:Le=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(n=Lc+300-vt(),10<n)){if(nl(a,t,it,!el),Bn(a,0,!0)!==0)break e;a.timeoutHandle=id(Ro.bind(null,a,l,Le,_u,qc,t,it,zl,da,el,u,2,-0,0),n);break e}Ro(a,l,Le,_u,qc,t,it,zl,da,el,u,0,-0,0)}}break}while(!0);St(e)}function Ro(e,t,l,a,n,u,f,o,m,b,j,N,p,S){if(e.timeoutHandle=-1,N=t.subtreeFlags,(N&8192||(N&16785408)===16785408)&&(xn={stylesheets:null,count:0,unsuspend:rm},No(t),N=dm(),N!==null)){e.cancelPendingCommit=N(Lo.bind(null,e,t,u,l,a,n,f,o,m,j,1,p,S)),nl(e,u,f,!b);return}Lo(e,t,u,l,a,n,f,o,m)}function Eh(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!Qe(u(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function nl(e,t,l,a){t&=~Bc,t&=~zl,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var u=31-Ge(n),f=1<<u;a[u]=-1,n&=~f}l!==0&&Gs(e,l,t)}function zu(){return(P&6)===0?(dn(0),!1):!0}function Xc(){if(k!==null){if(ee===0)var e=k.return;else e=k,Dt=Sl=null,ic(e),ca=null,tn=0,e=k;for(;e!==null;)ro(e.alternate,e),e=e.return;k=null}}function ya(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,Kh(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),Xc(),ce=e,k=l=zt(e.current,null),W=t,ee=0,Ve=null,el=!1,oa=za(e,t),Hc=!1,da=it=Bc=zl=tl=ve=0,Le=rn=null,qc=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-Ge(a),u=1<<n;t|=e[n],a&=~u}return qt=t,Wn(),l}function Uo(e,t){X=null,M.H=mu,t===Va||t===nu?(t=Wf(),ee=3):t===Vf?(t=Wf(),ee=4):ee=t===$r?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Ve=t,k===null&&(ve=1,bu(e,tt(t,e.current)))}function Co(){var e=M.H;return M.H=mu,e===null?mu:e}function wo(){var e=M.A;return M.A=zh,e}function Zc(){ve=4,el||(W&4194048)!==W&&ut.current!==null||(oa=!0),(tl&134217727)===0&&(zl&134217727)===0||ce===null||nl(ce,W,it,!1)}function kc(e,t,l){var a=P;P|=2;var n=Co(),u=wo();(ce!==e||W!==t)&&(_u=null,ya(e,t)),t=!1;var f=ve;e:do try{if(ee!==0&&k!==null){var o=k,m=Ve;switch(ee){case 8:Xc(),f=6;break e;case 3:case 2:case 9:case 6:ut.current===null&&(t=!0);var b=ee;if(ee=0,Ve=null,va(e,o,m,b),l&&oa){f=0;break e}break;default:b=ee,ee=0,Ve=null,va(e,o,m,b)}}Dh(),f=ve;break}catch(j){Uo(e,j)}while(!0);return t&&e.shellSuspendCounter++,Dt=Sl=null,P=a,M.H=n,M.A=u,k===null&&(ce=null,W=0,Wn()),f}function Dh(){for(;k!==null;)Ho(k)}function Oh(e,t){var l=P;P|=2;var a=Co(),n=wo();ce!==e||W!==t?(_u=null,Nu=vt()+500,ya(e,t)):oa=za(e,t);e:do try{if(ee!==0&&k!==null){t=k;var u=Ve;t:switch(ee){case 1:ee=0,Ve=null,va(e,t,u,1);break;case 2:case 9:if(Kf(u)){ee=0,Ve=null,Bo(t);break}t=function(){ee!==2&&ee!==9||ce!==e||(ee=7),St(e)},u.then(t,t);break e;case 3:ee=7;break e;case 4:ee=5;break e;case 7:Kf(u)?(ee=0,Ve=null,Bo(t)):(ee=0,Ve=null,va(e,t,u,7));break;case 5:var f=null;switch(k.tag){case 26:f=k.memoizedState;case 5:case 27:var o=k;if(!f||xd(f)){ee=0,Ve=null;var m=o.sibling;if(m!==null)k=m;else{var b=o.return;b!==null?(k=b,Au(b)):k=null}break t}}ee=0,Ve=null,va(e,t,u,5);break;case 6:ee=0,Ve=null,va(e,t,u,6);break;case 8:Xc(),ve=6;break e;default:throw Error(s(462))}}Rh();break}catch(j){Uo(e,j)}while(!0);return Dt=Sl=null,M.H=a,M.A=n,P=l,k!==null?0:(ce=null,W=0,Wn(),ve)}function Rh(){for(;k!==null&&!e0();)Ho(k)}function Ho(e){var t=so(e.alternate,e,qt);e.memoizedProps=e.pendingProps,t===null?Au(e):k=t}function Bo(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=lo(l,t,t.pendingProps,t.type,void 0,W);break;case 11:t=lo(l,t,t.pendingProps,t.type.render,t.ref,W);break;case 5:ic(t);default:ro(l,t),t=k=Bf(t,qt),t=so(l,t,qt)}e.memoizedProps=e.pendingProps,t===null?Au(e):k=t}function va(e,t,l,a){Dt=Sl=null,ic(t),ca=null,tn=0;var n=t.return;try{if(Sh(e,n,t,l,W)){ve=1,bu(e,tt(l,e.current)),k=null;return}}catch(u){if(n!==null)throw k=n,u;ve=1,bu(e,tt(l,e.current)),k=null;return}t.flags&32768?(I||a===1?e=!0:oa||(W&536870912)!==0?e=!1:(el=e=!0,(a===2||a===9||a===3||a===6)&&(a=ut.current,a!==null&&a.tag===13&&(a.flags|=16384))),qo(t,e)):Au(t)}function Au(e){var t=e;do{if((t.flags&32768)!==0){qo(t,el);return}e=t.return;var l=Th(t.alternate,t,qt);if(l!==null){k=l;return}if(t=t.sibling,t!==null){k=t;return}k=t=e}while(t!==null);ve===0&&(ve=5)}function qo(e,t){do{var l=Mh(e.alternate,e);if(l!==null){l.flags&=32767,k=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){k=e;return}k=e=l}while(e!==null);ve=6,k=null}function Lo(e,t,l,a,n,u,f,o,m){e.cancelPendingCommit=null;do Eu();while(Ee!==0);if((P&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(u=t.lanes|t.childLanes,u|=wi,r0(e,l,u,f,o,m),e===ce&&(k=ce=null,W=0),ha=t,al=e,ma=l,Yc=u,Gc=n,Eo=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Hh(Cn,function(){return Zo(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=M.T,M.T=null,n=E.p,E.p=2,f=P,P|=4;try{Nh(e,t,l)}finally{P=f,E.p=n,M.T=a}}Ee=1,Yo(),Go(),Qo()}}function Yo(){if(Ee===1){Ee=0;var e=al,t=ha,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var a=E.p;E.p=2;var n=P;P|=4;try{jo(t,e);var u=as,f=zf(e.containerInfo),o=u.focusedElem,m=u.selectionRange;if(f!==o&&o&&o.ownerDocument&&_f(o.ownerDocument.documentElement,o)){if(m!==null&&Di(o)){var b=m.start,j=m.end;if(j===void 0&&(j=b),"selectionStart"in o)o.selectionStart=b,o.selectionEnd=Math.min(j,o.value.length);else{var N=o.ownerDocument||document,p=N&&N.defaultView||window;if(p.getSelection){var S=p.getSelection(),L=o.textContent.length,H=Math.min(m.start,L),ne=m.end===void 0?H:Math.min(m.end,L);!S.extend&&H>ne&&(f=ne,ne=H,H=f);var v=Nf(o,H),y=Nf(o,ne);if(v&&y&&(S.rangeCount!==1||S.anchorNode!==v.node||S.anchorOffset!==v.offset||S.focusNode!==y.node||S.focusOffset!==y.offset)){var x=N.createRange();x.setStart(v.node,v.offset),S.removeAllRanges(),H>ne?(S.addRange(x),S.extend(y.node,y.offset)):(x.setEnd(y.node,y.offset),S.addRange(x))}}}}for(N=[],S=o;S=S.parentNode;)S.nodeType===1&&N.push({element:S,left:S.scrollLeft,top:S.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<N.length;o++){var T=N[o];T.element.scrollLeft=T.left,T.element.scrollTop=T.top}}Gu=!!ls,as=ls=null}finally{P=n,E.p=a,M.T=l}}e.current=t,Ee=2}}function Go(){if(Ee===2){Ee=0;var e=al,t=ha,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var a=E.p;E.p=2;var n=P;P|=4;try{xo(e,t.alternate,t)}finally{P=n,E.p=a,M.T=l}}Ee=3}}function Qo(){if(Ee===4||Ee===3){Ee=0,t0();var e=al,t=ha,l=ma,a=Eo;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?Ee=5:(Ee=0,ha=al=null,Xo(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(ll=null),fi(l),t=t.stateNode,Ye&&typeof Ye.onCommitFiberRoot=="function")try{Ye.onCommitFiberRoot(_a,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=M.T,n=E.p,E.p=2,M.T=null;try{for(var u=e.onRecoverableError,f=0;f<a.length;f++){var o=a[f];u(o.value,{componentStack:o.stack})}}finally{M.T=t,E.p=n}}(ma&3)!==0&&Eu(),St(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===Qc?on++:(on=0,Qc=e):on=0,dn(0)}}function Xo(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Za(t)))}function Eu(e){return Yo(),Go(),Qo(),Zo()}function Zo(){if(Ee!==5)return!1;var e=al,t=Yc;Yc=0;var l=fi(ma),a=M.T,n=E.p;try{E.p=32>l?32:l,M.T=null,l=Gc,Gc=null;var u=al,f=ma;if(Ee=0,ha=al=null,ma=0,(P&6)!==0)throw Error(s(331));var o=P;if(P|=4,zo(u.current),Mo(u,u.current,f,l),P=o,dn(0,!1),Ye&&typeof Ye.onPostCommitFiberRoot=="function")try{Ye.onPostCommitFiberRoot(_a,u)}catch{}return!0}finally{E.p=n,M.T=a,Xo(e,t)}}function ko(e,t,l){t=tt(l,t),t=pc(e.stateNode,t,2),e=Kt(e,t,2),e!==null&&(Aa(e,2),St(e))}function ue(e,t,l){if(e.tag===3)ko(e,e,l);else for(;t!==null;){if(t.tag===3){ko(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(ll===null||!ll.has(a))){e=tt(l,e),l=Jr(2),a=Kt(t,l,2),a!==null&&(Wr(l,a,t,e),Aa(a,2),St(a));break}}t=t.return}}function Vc(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new Ah;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(Hc=!0,n.add(l),e=Uh.bind(null,e,t,l),t.then(e,e))}function Uh(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,ce===e&&(W&l)===l&&(ve===4||ve===3&&(W&62914560)===W&&300>vt()-Lc?(P&2)===0&&ya(e,0):Bc|=l,da===W&&(da=0)),St(e)}function Vo(e,t){t===0&&(t=Ys()),e=Fl(e,t),e!==null&&(Aa(e,t),St(e))}function Ch(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Vo(e,l)}function wh(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(s(314))}a!==null&&a.delete(t),Vo(e,l)}function Hh(e,t){return ui(e,t)}var Du=null,ga=null,Kc=!1,Ou=!1,Jc=!1,Al=0;function St(e){e!==ga&&e.next===null&&(ga===null?Du=ga=e:ga=ga.next=e),Ou=!0,Kc||(Kc=!0,qh())}function dn(e,t){if(!Jc&&Ou){Jc=!0;do for(var l=!1,a=Du;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var f=a.suspendedLanes,o=a.pingedLanes;u=(1<<31-Ge(42|e)+1)-1,u&=n&~(f&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,$o(a,u))}else u=W,u=Bn(a,a===ce?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||za(a,u)||(l=!0,$o(a,u));a=a.next}while(l);Jc=!1}}function Bh(){Ko()}function Ko(){Ou=Kc=!1;var e=0;Al!==0&&(Vh()&&(e=Al),Al=0);for(var t=vt(),l=null,a=Du;a!==null;){var n=a.next,u=Jo(a,t);u===0?(a.next=null,l===null?Du=n:l.next=n,n===null&&(ga=l)):(l=a,(e!==0||(u&3)!==0)&&(Ou=!0)),a=n}dn(e)}function Jo(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var f=31-Ge(u),o=1<<f,m=n[f];m===-1?((o&l)===0||(o&a)!==0)&&(n[f]=f0(o,t)):m<=t&&(e.expiredLanes|=o),u&=~o}if(t=ce,l=W,l=Bn(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(ee===2||ee===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&ii(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||za(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&ii(a),fi(l)){case 2:case 8:l=Bs;break;case 32:l=Cn;break;case 268435456:l=qs;break;default:l=Cn}return a=Wo.bind(null,e),l=ui(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&ii(a),e.callbackPriority=2,e.callbackNode=null,2}function Wo(e,t){if(Ee!==0&&Ee!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(Eu()&&e.callbackNode!==l)return null;var a=W;return a=Bn(e,e===ce?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Oo(e,a,t),Jo(e,vt()),e.callbackNode!=null&&e.callbackNode===l?Wo.bind(null,e):null)}function $o(e,t){if(Eu())return null;Oo(e,t,!0)}function qh(){Jh(function(){(P&6)!==0?ui(Hs,Bh):Ko()})}function Wc(){return Al===0&&(Al=Ls()),Al}function Fo(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Qn(""+e)}function Io(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function Lh(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var u=Fo((n[we]||null).action),f=a.submitter;f&&(t=(t=f[we]||null)?Fo(t.formAction):f.getAttribute("formAction"),t!==null&&(u=t,f=null));var o=new Vn("action","action",null,a,n);e.push({event:o,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Al!==0){var m=f?Io(n,f):new FormData(n);yc(l,{pending:!0,data:m,method:n.method,action:u},null,m)}}else typeof u=="function"&&(o.preventDefault(),m=f?Io(n,f):new FormData(n),yc(l,{pending:!0,data:m,method:n.method,action:u},u,m))},currentTarget:n}]})}}for(var $c=0;$c<Ci.length;$c++){var Fc=Ci[$c],Yh=Fc.toLowerCase(),Gh=Fc[0].toUpperCase()+Fc.slice(1);rt(Yh,"on"+Gh)}rt(Df,"onAnimationEnd"),rt(Of,"onAnimationIteration"),rt(Rf,"onAnimationStart"),rt("dblclick","onDoubleClick"),rt("focusin","onFocus"),rt("focusout","onBlur"),rt(nh,"onTransitionRun"),rt(uh,"onTransitionStart"),rt(ih,"onTransitionCancel"),rt(Uf,"onTransitionEnd"),Gl("onMouseEnter",["mouseout","mouseover"]),Gl("onMouseLeave",["mouseout","mouseover"]),Gl("onPointerEnter",["pointerout","pointerover"]),Gl("onPointerLeave",["pointerout","pointerover"]),dl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),dl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),dl("onBeforeInput",["compositionend","keypress","textInput","paste"]),dl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),dl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),dl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var hn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Qh=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(hn));function Po(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var f=a.length-1;0<=f;f--){var o=a[f],m=o.instance,b=o.currentTarget;if(o=o.listener,m!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=b;try{u(n)}catch(j){xu(j)}n.currentTarget=null,u=m}else for(f=0;f<a.length;f++){if(o=a[f],m=o.instance,b=o.currentTarget,o=o.listener,m!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=b;try{u(n)}catch(j){xu(j)}n.currentTarget=null,u=m}}}}function V(e,t){var l=t[ri];l===void 0&&(l=t[ri]=new Set);var a=e+"__bubble";l.has(a)||(ed(t,e,2,!1),l.add(a))}function Ic(e,t,l){var a=0;t&&(a|=4),ed(l,e,a,t)}var Ru="_reactListening"+Math.random().toString(36).slice(2);function Pc(e){if(!e[Ru]){e[Ru]=!0,ks.forEach(function(l){l!=="selectionchange"&&(Qh.has(l)||Ic(l,!1,e),Ic(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ru]||(t[Ru]=!0,Ic("selectionchange",!1,t))}}function ed(e,t,l,a){switch(Md(t)){case 2:var n=ym;break;case 8:n=vm;break;default:n=hs}l=n.bind(null,t,l,e),n=void 0,!Si||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function es(e,t,l,a,n){var u=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var f=a.tag;if(f===3||f===4){var o=a.stateNode.containerInfo;if(o===n)break;if(f===4)for(f=a.return;f!==null;){var m=f.tag;if((m===3||m===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;o!==null;){if(f=ql(o),f===null)return;if(m=f.tag,m===5||m===6||m===26||m===27){a=u=f;continue e}o=o.parentNode}}a=a.return}uf(function(){var b=u,j=bi(l),N=[];e:{var p=Cf.get(e);if(p!==void 0){var S=Vn,L=e;switch(e){case"keypress":if(Zn(l)===0)break e;case"keydown":case"keyup":S=H0;break;case"focusin":L="focus",S=Ni;break;case"focusout":L="blur",S=Ni;break;case"beforeblur":case"afterblur":S=Ni;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=ff;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=M0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=L0;break;case Df:case Of:case Rf:S=z0;break;case Uf:S=G0;break;case"scroll":case"scrollend":S=j0;break;case"wheel":S=X0;break;case"copy":case"cut":case"paste":S=E0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=of;break;case"toggle":case"beforetoggle":S=k0}var H=(t&4)!==0,ne=!H&&(e==="scroll"||e==="scrollend"),v=H?p!==null?p+"Capture":null:p;H=[];for(var y=b,x;y!==null;){var T=y;if(x=T.stateNode,T=T.tag,T!==5&&T!==26&&T!==27||x===null||v===null||(T=Oa(y,v),T!=null&&H.push(mn(y,T,x))),ne)break;y=y.return}0<H.length&&(p=new S(p,L,null,l,j),N.push({event:p,listeners:H}))}}if((t&7)===0){e:{if(p=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",p&&l!==xi&&(L=l.relatedTarget||l.fromElement)&&(ql(L)||L[Bl]))break e;if((S||p)&&(p=j.window===j?j:(p=j.ownerDocument)?p.defaultView||p.parentWindow:window,S?(L=l.relatedTarget||l.toElement,S=b,L=L?ql(L):null,L!==null&&(ne=g(L),H=L.tag,L!==ne||H!==5&&H!==27&&H!==6)&&(L=null)):(S=null,L=b),S!==L)){if(H=ff,T="onMouseLeave",v="onMouseEnter",y="mouse",(e==="pointerout"||e==="pointerover")&&(H=of,T="onPointerLeave",v="onPointerEnter",y="pointer"),ne=S==null?p:Da(S),x=L==null?p:Da(L),p=new H(T,y+"leave",S,l,j),p.target=ne,p.relatedTarget=x,T=null,ql(j)===b&&(H=new H(v,y+"enter",L,l,j),H.target=x,H.relatedTarget=ne,T=H),ne=T,S&&L)t:{for(H=S,v=L,y=0,x=H;x;x=xa(x))y++;for(x=0,T=v;T;T=xa(T))x++;for(;0<y-x;)H=xa(H),y--;for(;0<x-y;)v=xa(v),x--;for(;y--;){if(H===v||v!==null&&H===v.alternate)break t;H=xa(H),v=xa(v)}H=null}else H=null;S!==null&&td(N,p,S,H,!1),L!==null&&ne!==null&&td(N,ne,L,H,!0)}}e:{if(p=b?Da(b):window,S=p.nodeName&&p.nodeName.toLowerCase(),S==="select"||S==="input"&&p.type==="file")var R=bf;else if(gf(p))if(pf)R=th;else{R=P0;var Z=I0}else S=p.nodeName,!S||S.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?b&&gi(b.elementType)&&(R=bf):R=eh;if(R&&(R=R(e,b))){xf(N,R,l,j);break e}Z&&Z(e,p,b),e==="focusout"&&b&&p.type==="number"&&b.memoizedProps.value!=null&&vi(p,"number",p.value)}switch(Z=b?Da(b):window,e){case"focusin":(gf(Z)||Z.contentEditable==="true")&&(Jl=Z,Oi=b,La=null);break;case"focusout":La=Oi=Jl=null;break;case"mousedown":Ri=!0;break;case"contextmenu":case"mouseup":case"dragend":Ri=!1,Af(N,l,j);break;case"selectionchange":if(ah)break;case"keydown":case"keyup":Af(N,l,j)}var U;if(zi)e:{switch(e){case"compositionstart":var B="onCompositionStart";break e;case"compositionend":B="onCompositionEnd";break e;case"compositionupdate":B="onCompositionUpdate";break e}B=void 0}else Kl?yf(e,l)&&(B="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(B="onCompositionStart");B&&(df&&l.locale!=="ko"&&(Kl||B!=="onCompositionStart"?B==="onCompositionEnd"&&Kl&&(U=cf()):(Xt=j,ji="value"in Xt?Xt.value:Xt.textContent,Kl=!0)),Z=Uu(b,B),0<Z.length&&(B=new rf(B,e,null,l,j),N.push({event:B,listeners:Z}),U?B.data=U:(U=vf(l),U!==null&&(B.data=U)))),(U=K0?J0(e,l):W0(e,l))&&(B=Uu(b,"onBeforeInput"),0<B.length&&(Z=new rf("onBeforeInput","beforeinput",null,l,j),N.push({event:Z,listeners:B}),Z.data=U)),Lh(N,e,b,l,j)}Po(N,t)})}function mn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function Uu(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Oa(e,l),n!=null&&a.unshift(mn(e,n,u)),n=Oa(e,t),n!=null&&a.push(mn(e,n,u))),e.tag===3)return a;e=e.return}return[]}function xa(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function td(e,t,l,a,n){for(var u=t._reactName,f=[];l!==null&&l!==a;){var o=l,m=o.alternate,b=o.stateNode;if(o=o.tag,m!==null&&m===a)break;o!==5&&o!==26&&o!==27||b===null||(m=b,n?(b=Oa(l,u),b!=null&&f.unshift(mn(l,b,m))):n||(b=Oa(l,u),b!=null&&f.push(mn(l,b,m)))),l=l.return}f.length!==0&&e.push({event:t,listeners:f})}var Xh=/\r\n?/g,Zh=/\u0000|\uFFFD/g;function ld(e){return(typeof e=="string"?e:""+e).replace(Xh,`
`).replace(Zh,"")}function ad(e,t){return t=ld(t),ld(e)===t}function Cu(){}function ae(e,t,l,a,n,u){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||Zl(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&Zl(e,""+a);break;case"className":Ln(e,"class",a);break;case"tabIndex":Ln(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ln(e,l,a);break;case"style":af(e,a,u);break;case"data":if(t!=="object"){Ln(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Qn(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(t!=="input"&&ae(e,t,"name",n.name,n,null),ae(e,t,"formEncType",n.formEncType,n,null),ae(e,t,"formMethod",n.formMethod,n,null),ae(e,t,"formTarget",n.formTarget,n,null)):(ae(e,t,"encType",n.encType,n,null),ae(e,t,"method",n.method,n,null),ae(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=Qn(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=Cu);break;case"onScroll":a!=null&&V("scroll",e);break;case"onScrollEnd":a!=null&&V("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=Qn(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":V("beforetoggle",e),V("toggle",e),qn(e,"popover",a);break;case"xlinkActuate":Nt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Nt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Nt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Nt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Nt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Nt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Nt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Nt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Nt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":qn(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=p0.get(l)||l,qn(e,l,a))}}function ts(e,t,l,a,n,u){switch(l){case"style":af(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(s(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(s(60));e.innerHTML=l}}break;case"children":typeof a=="string"?Zl(e,a):(typeof a=="number"||typeof a=="bigint")&&Zl(e,""+a);break;case"onScroll":a!=null&&V("scroll",e);break;case"onScrollEnd":a!=null&&V("scrollend",e);break;case"onClick":a!=null&&(e.onclick=Cu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Vs.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),u=e[we]||null,u=u!=null?u[l]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):qn(e,l,a)}}}function De(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":V("error",e),V("load",e);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var f=l[u];if(f!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:ae(e,t,u,f,l,null)}}n&&ae(e,t,"srcSet",l.srcSet,l,null),a&&ae(e,t,"src",l.src,l,null);return;case"input":V("invalid",e);var o=u=f=n=null,m=null,b=null;for(a in l)if(l.hasOwnProperty(a)){var j=l[a];if(j!=null)switch(a){case"name":n=j;break;case"type":f=j;break;case"checked":m=j;break;case"defaultChecked":b=j;break;case"value":u=j;break;case"defaultValue":o=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(s(137,t));break;default:ae(e,t,a,j,l,null)}}Ps(e,u,o,m,b,f,n,!1),Yn(e);return;case"select":V("invalid",e),a=f=u=null;for(n in l)if(l.hasOwnProperty(n)&&(o=l[n],o!=null))switch(n){case"value":u=o;break;case"defaultValue":f=o;break;case"multiple":a=o;default:ae(e,t,n,o,l,null)}t=u,l=f,e.multiple=!!a,t!=null?Xl(e,!!a,t,!1):l!=null&&Xl(e,!!a,l,!0);return;case"textarea":V("invalid",e),u=n=a=null;for(f in l)if(l.hasOwnProperty(f)&&(o=l[f],o!=null))switch(f){case"value":a=o;break;case"defaultValue":n=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(s(91));break;default:ae(e,t,f,o,l,null)}tf(e,a,n,u),Yn(e);return;case"option":for(m in l)if(l.hasOwnProperty(m)&&(a=l[m],a!=null))switch(m){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:ae(e,t,m,a,l,null)}return;case"dialog":V("beforetoggle",e),V("toggle",e),V("cancel",e),V("close",e);break;case"iframe":case"object":V("load",e);break;case"video":case"audio":for(a=0;a<hn.length;a++)V(hn[a],e);break;case"image":V("error",e),V("load",e);break;case"details":V("toggle",e);break;case"embed":case"source":case"link":V("error",e),V("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(b in l)if(l.hasOwnProperty(b)&&(a=l[b],a!=null))switch(b){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:ae(e,t,b,a,l,null)}return;default:if(gi(t)){for(j in l)l.hasOwnProperty(j)&&(a=l[j],a!==void 0&&ts(e,t,j,a,l,void 0));return}}for(o in l)l.hasOwnProperty(o)&&(a=l[o],a!=null&&ae(e,t,o,a,l,null))}function kh(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,f=null,o=null,m=null,b=null,j=null;for(S in l){var N=l[S];if(l.hasOwnProperty(S)&&N!=null)switch(S){case"checked":break;case"value":break;case"defaultValue":m=N;default:a.hasOwnProperty(S)||ae(e,t,S,null,a,N)}}for(var p in a){var S=a[p];if(N=l[p],a.hasOwnProperty(p)&&(S!=null||N!=null))switch(p){case"type":u=S;break;case"name":n=S;break;case"checked":b=S;break;case"defaultChecked":j=S;break;case"value":f=S;break;case"defaultValue":o=S;break;case"children":case"dangerouslySetInnerHTML":if(S!=null)throw Error(s(137,t));break;default:S!==N&&ae(e,t,p,S,a,N)}}yi(e,f,o,m,b,j,u,n);return;case"select":S=f=o=p=null;for(u in l)if(m=l[u],l.hasOwnProperty(u)&&m!=null)switch(u){case"value":break;case"multiple":S=m;default:a.hasOwnProperty(u)||ae(e,t,u,null,a,m)}for(n in a)if(u=a[n],m=l[n],a.hasOwnProperty(n)&&(u!=null||m!=null))switch(n){case"value":p=u;break;case"defaultValue":o=u;break;case"multiple":f=u;default:u!==m&&ae(e,t,n,u,a,m)}t=o,l=f,a=S,p!=null?Xl(e,!!l,p,!1):!!a!=!!l&&(t!=null?Xl(e,!!l,t,!0):Xl(e,!!l,l?[]:"",!1));return;case"textarea":S=p=null;for(o in l)if(n=l[o],l.hasOwnProperty(o)&&n!=null&&!a.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:ae(e,t,o,null,a,n)}for(f in a)if(n=a[f],u=l[f],a.hasOwnProperty(f)&&(n!=null||u!=null))switch(f){case"value":p=n;break;case"defaultValue":S=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==u&&ae(e,t,f,n,a,u)}ef(e,p,S);return;case"option":for(var L in l)if(p=l[L],l.hasOwnProperty(L)&&p!=null&&!a.hasOwnProperty(L))switch(L){case"selected":e.selected=!1;break;default:ae(e,t,L,null,a,p)}for(m in a)if(p=a[m],S=l[m],a.hasOwnProperty(m)&&p!==S&&(p!=null||S!=null))switch(m){case"selected":e.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:ae(e,t,m,p,a,S)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var H in l)p=l[H],l.hasOwnProperty(H)&&p!=null&&!a.hasOwnProperty(H)&&ae(e,t,H,null,a,p);for(b in a)if(p=a[b],S=l[b],a.hasOwnProperty(b)&&p!==S&&(p!=null||S!=null))switch(b){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(137,t));break;default:ae(e,t,b,p,a,S)}return;default:if(gi(t)){for(var ne in l)p=l[ne],l.hasOwnProperty(ne)&&p!==void 0&&!a.hasOwnProperty(ne)&&ts(e,t,ne,void 0,a,p);for(j in a)p=a[j],S=l[j],!a.hasOwnProperty(j)||p===S||p===void 0&&S===void 0||ts(e,t,j,p,a,S);return}}for(var v in l)p=l[v],l.hasOwnProperty(v)&&p!=null&&!a.hasOwnProperty(v)&&ae(e,t,v,null,a,p);for(N in a)p=a[N],S=l[N],!a.hasOwnProperty(N)||p===S||p==null&&S==null||ae(e,t,N,p,a,S)}var ls=null,as=null;function wu(e){return e.nodeType===9?e:e.ownerDocument}function nd(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ud(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function ns(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var us=null;function Vh(){var e=window.event;return e&&e.type==="popstate"?e===us?!1:(us=e,!0):(us=null,!1)}var id=typeof setTimeout=="function"?setTimeout:void 0,Kh=typeof clearTimeout=="function"?clearTimeout:void 0,cd=typeof Promise=="function"?Promise:void 0,Jh=typeof queueMicrotask=="function"?queueMicrotask:typeof cd<"u"?function(e){return cd.resolve(null).then(e).catch(Wh)}:id;function Wh(e){setTimeout(function(){throw e})}function ul(e){return e==="head"}function sd(e,t){var l=t,a=0,n=0;do{var u=l.nextSibling;if(e.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var f=e.ownerDocument;if(l&1&&yn(f.documentElement),l&2&&yn(f.body),l&4)for(l=f.head,yn(l),f=l.firstChild;f;){var o=f.nextSibling,m=f.nodeName;f[Ea]||m==="SCRIPT"||m==="STYLE"||m==="LINK"&&f.rel.toLowerCase()==="stylesheet"||l.removeChild(f),f=o}}if(n===0){e.removeChild(u),Tn(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);Tn(t)}function is(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":is(l),oi(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function $h(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Ea])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=dt(e.nextSibling),e===null)break}return null}function Fh(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=dt(e.nextSibling),e===null))return null;return e}function cs(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Ih(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var ss=null;function fd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function rd(e,t,l){switch(t=wu(l),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function yn(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);oi(e)}var ct=new Map,od=new Set;function Hu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Lt=E.d;E.d={f:Ph,r:em,D:tm,C:lm,L:am,m:nm,X:im,S:um,M:cm};function Ph(){var e=Lt.f(),t=zu();return e||t}function em(e){var t=Ll(e);t!==null&&t.tag===5&&t.type==="form"?Dr(t):Lt.r(e)}var ba=typeof document>"u"?null:document;function dd(e,t,l){var a=ba;if(a&&typeof t=="string"&&t){var n=et(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),od.has(n)||(od.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),De(t,"link",e),Me(t),a.head.appendChild(t)))}}function tm(e){Lt.D(e),dd("dns-prefetch",e,null)}function lm(e,t){Lt.C(e,t),dd("preconnect",e,t)}function am(e,t,l){Lt.L(e,t,l);var a=ba;if(a&&e&&t){var n='link[rel="preload"][as="'+et(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+et(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+et(l.imageSizes)+'"]')):n+='[href="'+et(e)+'"]';var u=n;switch(t){case"style":u=pa(e);break;case"script":u=Sa(e)}ct.has(u)||(e=A({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),ct.set(u,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(vn(u))||t==="script"&&a.querySelector(gn(u))||(t=a.createElement("link"),De(t,"link",e),Me(t),a.head.appendChild(t)))}}function nm(e,t){Lt.m(e,t);var l=ba;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+et(a)+'"][href="'+et(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Sa(e)}if(!ct.has(u)&&(e=A({rel:"modulepreload",href:e},t),ct.set(u,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(gn(u)))return}a=l.createElement("link"),De(a,"link",e),Me(a),l.head.appendChild(a)}}}function um(e,t,l){Lt.S(e,t,l);var a=ba;if(a&&e){var n=Yl(a).hoistableStyles,u=pa(e);t=t||"default";var f=n.get(u);if(!f){var o={loading:0,preload:null};if(f=a.querySelector(vn(u)))o.loading=5;else{e=A({rel:"stylesheet",href:e,"data-precedence":t},l),(l=ct.get(u))&&fs(e,l);var m=f=a.createElement("link");Me(m),De(m,"link",e),m._p=new Promise(function(b,j){m.onload=b,m.onerror=j}),m.addEventListener("load",function(){o.loading|=1}),m.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Bu(f,t,a)}f={type:"stylesheet",instance:f,count:1,state:o},n.set(u,f)}}}function im(e,t){Lt.X(e,t);var l=ba;if(l&&e){var a=Yl(l).hoistableScripts,n=Sa(e),u=a.get(n);u||(u=l.querySelector(gn(n)),u||(e=A({src:e,async:!0},t),(t=ct.get(n))&&rs(e,t),u=l.createElement("script"),Me(u),De(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function cm(e,t){Lt.M(e,t);var l=ba;if(l&&e){var a=Yl(l).hoistableScripts,n=Sa(e),u=a.get(n);u||(u=l.querySelector(gn(n)),u||(e=A({src:e,async:!0,type:"module"},t),(t=ct.get(n))&&rs(e,t),u=l.createElement("script"),Me(u),De(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function hd(e,t,l,a){var n=(n=Yt.current)?Hu(n):null;if(!n)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=pa(l.href),l=Yl(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=pa(l.href);var u=Yl(n).hoistableStyles,f=u.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,f),(u=n.querySelector(vn(e)))&&!u._p&&(f.instance=u,f.state.loading=5),ct.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ct.set(e,l),u||sm(n,e,l,f.state))),t&&a===null)throw Error(s(528,""));return f}if(t&&a!==null)throw Error(s(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Sa(l),l=Yl(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function pa(e){return'href="'+et(e)+'"'}function vn(e){return'link[rel="stylesheet"]['+e+"]"}function md(e){return A({},e,{"data-precedence":e.precedence,precedence:null})}function sm(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),De(t,"link",l),Me(t),e.head.appendChild(t))}function Sa(e){return'[src="'+et(e)+'"]'}function gn(e){return"script[async]"+e}function yd(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+et(l.href)+'"]');if(a)return t.instance=a,Me(a),a;var n=A({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Me(a),De(a,"style",n),Bu(a,l.precedence,e),t.instance=a;case"stylesheet":n=pa(l.href);var u=e.querySelector(vn(n));if(u)return t.state.loading|=4,t.instance=u,Me(u),u;a=md(l),(n=ct.get(n))&&fs(a,n),u=(e.ownerDocument||e).createElement("link"),Me(u);var f=u;return f._p=new Promise(function(o,m){f.onload=o,f.onerror=m}),De(u,"link",a),t.state.loading|=4,Bu(u,l.precedence,e),t.instance=u;case"script":return u=Sa(l.src),(n=e.querySelector(gn(u)))?(t.instance=n,Me(n),n):(a=l,(n=ct.get(u))&&(a=A({},l),rs(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),Me(n),De(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,Bu(a,l.precedence,e));return t.instance}function Bu(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,f=0;f<a.length;f++){var o=a[f];if(o.dataset.precedence===t)u=o;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function fs(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function rs(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var qu=null;function vd(e,t,l){if(qu===null){var a=new Map,n=qu=new Map;n.set(l,a)}else n=qu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var u=l[n];if(!(u[Ea]||u[Oe]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(t)||"";f=e+f;var o=a.get(f);o?o.push(u):a.set(f,[u])}}return a}function gd(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function fm(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function xd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var xn=null;function rm(){}function om(e,t,l){if(xn===null)throw Error(s(475));var a=xn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=pa(l.href),u=e.querySelector(vn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=Lu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,Me(u);return}u=e.ownerDocument||e,l=md(l),(n=ct.get(n))&&fs(l,n),u=u.createElement("link"),Me(u);var f=u;f._p=new Promise(function(o,m){f.onload=o,f.onerror=m}),De(u,"link",l),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=Lu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function dm(){if(xn===null)throw Error(s(475));var e=xn;return e.stylesheets&&e.count===0&&os(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&os(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function Lu(){if(this.count--,this.count===0){if(this.stylesheets)os(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Yu=null;function os(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Yu=new Map,t.forEach(hm,e),Yu=null,Lu.call(e))}function hm(e,t){if(!(t.state.loading&4)){var l=Yu.get(e);if(l)var a=l.get(null);else{l=new Map,Yu.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var f=n[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),a=f)}a&&l.set(null,a)}n=t.instance,f=n.getAttribute("data-precedence"),u=l.get(f)||a,u===a&&l.set(null,n),l.set(f,n),this.count++,a=Lu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var bn={$$typeof:ie,Provider:null,Consumer:null,_currentValue:Y,_currentValue2:Y,_threadCount:0};function mm(e,t,l,a,n,u,f,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ci(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ci(0),this.hiddenUpdates=ci(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function bd(e,t,l,a,n,u,f,o,m,b,j,N){return e=new mm(e,t,l,f,o,m,b,N),t=1,u===!0&&(t|=24),u=Xe(3,null,null,t),e.current=u,u.stateNode=e,t=Vi(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:t},$i(u),e}function pd(e){return e?(e=Il,e):Il}function Sd(e,t,l,a,n,u){n=pd(n),a.context===null?a.context=n:a.pendingContext=n,a=Vt(t),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=Kt(e,a,t),l!==null&&(Je(l,e,t),Ja(l,e,t))}function jd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function ds(e,t){jd(e,t),(e=e.alternate)&&jd(e,t)}function Td(e){if(e.tag===13){var t=Fl(e,67108864);t!==null&&Je(t,e,67108864),ds(e,67108864)}}var Gu=!0;function ym(e,t,l,a){var n=M.T;M.T=null;var u=E.p;try{E.p=2,hs(e,t,l,a)}finally{E.p=u,M.T=n}}function vm(e,t,l,a){var n=M.T;M.T=null;var u=E.p;try{E.p=8,hs(e,t,l,a)}finally{E.p=u,M.T=n}}function hs(e,t,l,a){if(Gu){var n=ms(a);if(n===null)es(e,t,a,Qu,l),Nd(e,a);else if(xm(n,e,t,l,a))a.stopPropagation();else if(Nd(e,a),t&4&&-1<gm.indexOf(e)){for(;n!==null;){var u=Ll(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=ol(u.pendingLanes);if(f!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;f;){var m=1<<31-Ge(f);o.entanglements[1]|=m,f&=~m}St(u),(P&6)===0&&(Nu=vt()+500,dn(0))}}break;case 13:o=Fl(u,2),o!==null&&Je(o,u,2),zu(),ds(u,2)}if(u=ms(a),u===null&&es(e,t,a,Qu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else es(e,t,a,null,l)}}function ms(e){return e=bi(e),ys(e)}var Qu=null;function ys(e){if(Qu=null,e=ql(e),e!==null){var t=g(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=_(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Qu=e,null}function Md(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(l0()){case Hs:return 2;case Bs:return 8;case Cn:case a0:return 32;case qs:return 268435456;default:return 32}default:return 32}}var vs=!1,il=null,cl=null,sl=null,pn=new Map,Sn=new Map,fl=[],gm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Nd(e,t){switch(e){case"focusin":case"focusout":il=null;break;case"dragenter":case"dragleave":cl=null;break;case"mouseover":case"mouseout":sl=null;break;case"pointerover":case"pointerout":pn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Sn.delete(t.pointerId)}}function jn(e,t,l,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},t!==null&&(t=Ll(t),t!==null&&Td(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function xm(e,t,l,a,n){switch(t){case"focusin":return il=jn(il,e,t,l,a,n),!0;case"dragenter":return cl=jn(cl,e,t,l,a,n),!0;case"mouseover":return sl=jn(sl,e,t,l,a,n),!0;case"pointerover":var u=n.pointerId;return pn.set(u,jn(pn.get(u)||null,e,t,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,Sn.set(u,jn(Sn.get(u)||null,e,t,l,a,n)),!0}return!1}function _d(e){var t=ql(e.target);if(t!==null){var l=g(t);if(l!==null){if(t=l.tag,t===13){if(t=_(l),t!==null){e.blockedOn=t,o0(e.priority,function(){if(l.tag===13){var a=Ke();a=si(a);var n=Fl(l,a);n!==null&&Je(n,l,a),ds(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Xu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=ms(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);xi=a,l.target.dispatchEvent(a),xi=null}else return t=Ll(l),t!==null&&Td(t),e.blockedOn=l,!1;t.shift()}return!0}function zd(e,t,l){Xu(e)&&l.delete(t)}function bm(){vs=!1,il!==null&&Xu(il)&&(il=null),cl!==null&&Xu(cl)&&(cl=null),sl!==null&&Xu(sl)&&(sl=null),pn.forEach(zd),Sn.forEach(zd)}function Zu(e,t){e.blockedOn===t&&(e.blockedOn=null,vs||(vs=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,bm)))}var ku=null;function Ad(e){ku!==e&&(ku=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){ku===e&&(ku=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(ys(a||l)===null)continue;break}var u=Ll(l);u!==null&&(e.splice(t,3),t-=3,yc(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function Tn(e){function t(m){return Zu(m,e)}il!==null&&Zu(il,e),cl!==null&&Zu(cl,e),sl!==null&&Zu(sl,e),pn.forEach(t),Sn.forEach(t);for(var l=0;l<fl.length;l++){var a=fl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<fl.length&&(l=fl[0],l.blockedOn===null);)_d(l),l.blockedOn===null&&fl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],f=n[we]||null;if(typeof u=="function")f||Ad(l);else if(f){var o=null;if(u&&u.hasAttribute("formAction")){if(n=u,f=u[we]||null)o=f.formAction;else if(ys(n)!==null)continue}else o=f.action;typeof o=="function"?l[a+1]=o:(l.splice(a,3),a-=3),Ad(l)}}}function gs(e){this._internalRoot=e}Vu.prototype.render=gs.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var l=t.current,a=Ke();Sd(l,a,e,t,null,null)},Vu.prototype.unmount=gs.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Sd(e.current,2,null,e,null,null),zu(),t[Bl]=null}};function Vu(e){this._internalRoot=e}Vu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xs();e={blockedOn:null,target:e,priority:t};for(var l=0;l<fl.length&&t!==0&&t<fl[l].priority;l++);fl.splice(l,0,e),l===0&&_d(e)}};var Ed=c.version;if(Ed!=="19.1.1")throw Error(s(527,Ed,"19.1.1"));E.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=O(t),e=e!==null?D(e):null,e=e===null?null:e.stateNode,e};var pm={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ku=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ku.isDisabled&&Ku.supportsFiber)try{_a=Ku.inject(pm),Ye=Ku}catch{}}return Nn.createRoot=function(e,t){if(!h(e))throw Error(s(299));var l=!1,a="",n=Zr,u=kr,f=Vr,o=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(o=t.unstable_transitionCallbacks)),t=bd(e,1,!1,null,null,l,a,n,u,f,o,null),e[Bl]=t.current,Pc(e),new gs(t)},Nn.hydrateRoot=function(e,t,l){if(!h(e))throw Error(s(299));var a=!1,n="",u=Zr,f=kr,o=Vr,m=null,b=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(o=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(m=l.unstable_transitionCallbacks),l.formState!==void 0&&(b=l.formState)),t=bd(e,1,!0,t,l??null,a,n,u,f,o,m,b),t.context=pd(null),l=t.current,a=Ke(),a=si(a),n=Vt(a),n.callback=null,Kt(l,n,a),l=a,t.current.lanes=l,Aa(t,l),St(t),e[Bl]=t.current,Pc(e),new Vu(t)},Nn.version="19.1.1",Nn}var wd;function Om(){if(wd)return bs.exports;wd=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),bs.exports=Dm(),bs.exports}var Rm=Om();function kd(i){var c,r,s="";if(typeof i=="string"||typeof i=="number")s+=i;else if(typeof i=="object")if(Array.isArray(i)){var h=i.length;for(c=0;c<h;c++)i[c]&&(r=kd(i[c]))&&(s&&(s+=" "),s+=r)}else for(r in i)i[r]&&(s&&(s+=" "),s+=r);return s}function q(){for(var i,c,r=0,s="",h=arguments.length;r<h;r++)(i=arguments[r])&&(c=kd(i))&&(s&&(s+=" "),s+=c);return s}const Hd={BASE_URL:"/",DEV:!1,MODE:"production",PROD:!0,SSR:!1,VITE_API_BASE_URL:"http://localhost:8089"},_n=new Map,Ju=i=>{const c=_n.get(i);return c?Object.fromEntries(Object.entries(c.stores).map(([r,s])=>[r,s.getState()])):{}},Um=(i,c,r)=>{if(i===void 0)return{type:"untracked",connection:c.connect(r)};const s=_n.get(r.name);if(s)return{type:"tracked",store:i,...s};const h={connection:c.connect(r),stores:{}};return _n.set(r.name,h),{type:"tracked",store:i,...h}},Cm=(i,c)=>{if(c===void 0)return;const r=_n.get(i);r&&(delete r.stores[c],Object.keys(r.stores).length===0&&_n.delete(i))},wm=i=>{var c,r;if(!i)return;const s=i.split(`
`),h=s.findIndex(_=>_.includes("api.setState"));if(h<0)return;const g=((c=s[h+1])==null?void 0:c.trim())||"";return(r=/.+ (.+) .+/.exec(g))==null?void 0:r[1]},Hm=(i,c={})=>(r,s,h)=>{const{enabled:g,anonymousActionType:_,store:z,...O}=c;let D;try{D=(g??(Hd?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!D)return i(r,s,h);const{connection:A,...K}=Um(z,D,O);let G=!0;h.setState=(C,w,J)=>{const $=r(C,w);if(!G)return $;const ie=J===void 0?{type:_||wm(new Error().stack)||"anonymous"}:typeof J=="string"?{type:J}:J;return z===void 0?(A==null||A.send(ie,s()),$):(A==null||A.send({...ie,type:`${z}/${ie.type}`},{...Ju(O.name),[z]:h.getState()}),$)},h.devtools={cleanup:()=>{A&&typeof A.unsubscribe=="function"&&A.unsubscribe(),Cm(O.name,z)}};const Q=(...C)=>{const w=G;G=!1,r(...C),G=w},oe=i(h.setState,s,h);if(K.type==="untracked"?A==null||A.init(oe):(K.stores[K.store]=h,A==null||A.init(Object.fromEntries(Object.entries(K.stores).map(([C,w])=>[C,C===K.store?oe:w.getState()])))),h.dispatchFromDevtools&&typeof h.dispatch=="function"){let C=!1;const w=h.dispatch;h.dispatch=(...J)=>{(Hd?"production":void 0)!=="production"&&J[0].type==="__setState"&&!C&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),C=!0),w(...J)}}return A.subscribe(C=>{var w;switch(C.type){case"ACTION":if(typeof C.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return js(C.payload,J=>{if(J.type==="__setState"){if(z===void 0){Q(J.state);return}Object.keys(J.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const $=J.state[z];if($==null)return;JSON.stringify(h.getState())!==JSON.stringify($)&&Q($);return}h.dispatchFromDevtools&&typeof h.dispatch=="function"&&h.dispatch(J)});case"DISPATCH":switch(C.payload.type){case"RESET":return Q(oe),z===void 0?A==null?void 0:A.init(h.getState()):A==null?void 0:A.init(Ju(O.name));case"COMMIT":if(z===void 0){A==null||A.init(h.getState());return}return A==null?void 0:A.init(Ju(O.name));case"ROLLBACK":return js(C.state,J=>{if(z===void 0){Q(J),A==null||A.init(h.getState());return}Q(J[z]),A==null||A.init(Ju(O.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return js(C.state,J=>{if(z===void 0){Q(J);return}JSON.stringify(h.getState())!==JSON.stringify(J[z])&&Q(J[z])});case"IMPORT_STATE":{const{nextLiftedState:J}=C.payload,$=(w=J.computedStates.slice(-1)[0])==null?void 0:w.state;if(!$)return;Q(z===void 0?$:$[z]),A==null||A.send(null,J);return}case"PAUSE_RECORDING":return G=!G}return}}),oe},Vd=Hm,js=(i,c)=>{let r;try{r=JSON.parse(i)}catch(s){console.error("[zustand devtools middleware] Could not parse the received json",s)}r!==void 0&&c(r)};function Bm(i,c){let r;try{r=i()}catch{return}return{getItem:h=>{var g;const _=O=>O===null?null:JSON.parse(O,void 0),z=(g=r.getItem(h))!=null?g:null;return z instanceof Promise?z.then(_):_(z)},setItem:(h,g)=>r.setItem(h,JSON.stringify(g,void 0)),removeItem:h=>r.removeItem(h)}}const Ns=i=>c=>{try{const r=i(c);return r instanceof Promise?r:{then(s){return Ns(s)(r)},catch(s){return this}}}catch(r){return{then(s){return this},catch(s){return Ns(s)(r)}}}},qm=(i,c)=>(r,s,h)=>{let g={storage:Bm(()=>localStorage),partialize:C=>C,version:0,merge:(C,w)=>({...w,...C}),...c},_=!1;const z=new Set,O=new Set;let D=g.storage;if(!D)return i((...C)=>{console.warn(`[zustand persist middleware] Unable to update item '${g.name}', the given storage is currently unavailable.`),r(...C)},s,h);const A=()=>{const C=g.partialize({...s()});return D.setItem(g.name,{state:C,version:g.version})},K=h.setState;h.setState=(C,w)=>{K(C,w),A()};const G=i((...C)=>{r(...C),A()},s,h);h.getInitialState=()=>G;let Q;const oe=()=>{var C,w;if(!D)return;_=!1,z.forEach($=>{var ie;return $((ie=s())!=null?ie:G)});const J=((w=g.onRehydrateStorage)==null?void 0:w.call(g,(C=s())!=null?C:G))||void 0;return Ns(D.getItem.bind(D))(g.name).then($=>{if($)if(typeof $.version=="number"&&$.version!==g.version){if(g.migrate){const ie=g.migrate($.state,$.version);return ie instanceof Promise?ie.then(Fe=>[!0,Fe]):[!0,ie]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,$.state];return[!1,void 0]}).then($=>{var ie;const[Fe,jt]=$;if(Q=g.merge(jt,(ie=s())!=null?ie:G),r(Q,!0),Fe)return A()}).then(()=>{J==null||J(Q,void 0),Q=s(),_=!0,O.forEach($=>$(Q))}).catch($=>{J==null||J(void 0,$)})};return h.persist={setOptions:C=>{g={...g,...C},C.storage&&(D=C.storage)},clearStorage:()=>{D==null||D.removeItem(g.name)},getOptions:()=>g,rehydrate:()=>oe(),hasHydrated:()=>_,onHydrate:C=>(z.add(C),()=>{z.delete(C)}),onFinishHydration:C=>(O.add(C),()=>{O.delete(C)})},g.skipHydration||oe(),Q||G},Lm=qm;var Kd=Symbol.for("immer-nothing"),Bd=Symbol.for("immer-draftable"),We=Symbol.for("immer-state");function ht(i,...c){throw new Error(`[Immer] minified error nr: ${i}. Full error at: https://bit.ly/3cXEKWf`)}var Ta=Object.getPrototypeOf;function Ma(i){return!!i&&!!i[We]}function Rl(i){var c;return i?Jd(i)||Array.isArray(i)||!!i[Bd]||!!((c=i.constructor)!=null&&c[Bd])||ei(i)||ti(i):!1}var Ym=Object.prototype.constructor.toString();function Jd(i){if(!i||typeof i!="object")return!1;const c=Ta(i);if(c===null)return!0;const r=Object.hasOwnProperty.call(c,"constructor")&&c.constructor;return r===Object?!0:typeof r=="function"&&Function.toString.call(r)===Ym}function $u(i,c){Pu(i)===0?Reflect.ownKeys(i).forEach(r=>{c(r,i[r],i)}):i.forEach((r,s)=>c(s,r,i))}function Pu(i){const c=i[We];return c?c.type_:Array.isArray(i)?1:ei(i)?2:ti(i)?3:0}function _s(i,c){return Pu(i)===2?i.has(c):Object.prototype.hasOwnProperty.call(i,c)}function Wd(i,c,r){const s=Pu(i);s===2?i.set(c,r):s===3?i.add(r):i[c]=r}function Gm(i,c){return i===c?i!==0||1/i===1/c:i!==i&&c!==c}function ei(i){return i instanceof Map}function ti(i){return i instanceof Set}function Dl(i){return i.copy_||i.base_}function zs(i,c){if(ei(i))return new Map(i);if(ti(i))return new Set(i);if(Array.isArray(i))return Array.prototype.slice.call(i);const r=Jd(i);if(c===!0||c==="class_only"&&!r){const s=Object.getOwnPropertyDescriptors(i);delete s[We];let h=Reflect.ownKeys(s);for(let g=0;g<h.length;g++){const _=h[g],z=s[_];z.writable===!1&&(z.writable=!0,z.configurable=!0),(z.get||z.set)&&(s[_]={configurable:!0,writable:!0,enumerable:z.enumerable,value:i[_]})}return Object.create(Ta(i),s)}else{const s=Ta(i);if(s!==null&&r)return{...i};const h=Object.create(s);return Object.assign(h,i)}}function Rs(i,c=!1){return li(i)||Ma(i)||!Rl(i)||(Pu(i)>1&&(i.set=i.add=i.clear=i.delete=Qm),Object.freeze(i),c&&Object.entries(i).forEach(([r,s])=>Rs(s,!0))),i}function Qm(){ht(2)}function li(i){return Object.isFrozen(i)}var Xm={};function Ul(i){const c=Xm[i];return c||ht(0,i),c}var zn;function $d(){return zn}function Zm(i,c){return{drafts_:[],parent_:i,immer_:c,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function qd(i,c){c&&(Ul("Patches"),i.patches_=[],i.inversePatches_=[],i.patchListener_=c)}function As(i){Es(i),i.drafts_.forEach(km),i.drafts_=null}function Es(i){i===zn&&(zn=i.parent_)}function Ld(i){return zn=Zm(zn,i)}function km(i){const c=i[We];c.type_===0||c.type_===1?c.revoke_():c.revoked_=!0}function Yd(i,c){c.unfinalizedDrafts_=c.drafts_.length;const r=c.drafts_[0];return i!==void 0&&i!==r?(r[We].modified_&&(As(c),ht(4)),Rl(i)&&(i=Fu(c,i),c.parent_||Iu(c,i)),c.patches_&&Ul("Patches").generateReplacementPatches_(r[We].base_,i,c.patches_,c.inversePatches_)):i=Fu(c,r,[]),As(c),c.patches_&&c.patchListener_(c.patches_,c.inversePatches_),i!==Kd?i:void 0}function Fu(i,c,r){if(li(c))return c;const s=c[We];if(!s)return $u(c,(h,g)=>Gd(i,s,c,h,g,r)),c;if(s.scope_!==i)return c;if(!s.modified_)return Iu(i,s.base_,!0),s.base_;if(!s.finalized_){s.finalized_=!0,s.scope_.unfinalizedDrafts_--;const h=s.copy_;let g=h,_=!1;s.type_===3&&(g=new Set(h),h.clear(),_=!0),$u(g,(z,O)=>Gd(i,s,h,z,O,r,_)),Iu(i,h,!1),r&&i.patches_&&Ul("Patches").generatePatches_(s,r,i.patches_,i.inversePatches_)}return s.copy_}function Gd(i,c,r,s,h,g,_){if(Ma(h)){const z=g&&c&&c.type_!==3&&!_s(c.assigned_,s)?g.concat(s):void 0,O=Fu(i,h,z);if(Wd(r,s,O),Ma(O))i.canAutoFreeze_=!1;else return}else _&&r.add(h);if(Rl(h)&&!li(h)){if(!i.immer_.autoFreeze_&&i.unfinalizedDrafts_<1)return;Fu(i,h),(!c||!c.scope_.parent_)&&typeof s!="symbol"&&Object.prototype.propertyIsEnumerable.call(r,s)&&Iu(i,h)}}function Iu(i,c,r=!1){!i.parent_&&i.immer_.autoFreeze_&&i.canAutoFreeze_&&Rs(c,r)}function Vm(i,c){const r=Array.isArray(i),s={type_:r?1:0,scope_:c?c.scope_:$d(),modified_:!1,finalized_:!1,assigned_:{},parent_:c,base_:i,draft_:null,copy_:null,revoke_:null,isManual_:!1};let h=s,g=Us;r&&(h=[s],g=An);const{revoke:_,proxy:z}=Proxy.revocable(h,g);return s.draft_=z,s.revoke_=_,z}var Us={get(i,c){if(c===We)return i;const r=Dl(i);if(!_s(r,c))return Km(i,r,c);const s=r[c];return i.finalized_||!Rl(s)?s:s===Ts(i.base_,c)?(Ms(i),i.copy_[c]=Os(s,i)):s},has(i,c){return c in Dl(i)},ownKeys(i){return Reflect.ownKeys(Dl(i))},set(i,c,r){const s=Fd(Dl(i),c);if(s!=null&&s.set)return s.set.call(i.draft_,r),!0;if(!i.modified_){const h=Ts(Dl(i),c),g=h==null?void 0:h[We];if(g&&g.base_===r)return i.copy_[c]=r,i.assigned_[c]=!1,!0;if(Gm(r,h)&&(r!==void 0||_s(i.base_,c)))return!0;Ms(i),Ds(i)}return i.copy_[c]===r&&(r!==void 0||c in i.copy_)||Number.isNaN(r)&&Number.isNaN(i.copy_[c])||(i.copy_[c]=r,i.assigned_[c]=!0),!0},deleteProperty(i,c){return Ts(i.base_,c)!==void 0||c in i.base_?(i.assigned_[c]=!1,Ms(i),Ds(i)):delete i.assigned_[c],i.copy_&&delete i.copy_[c],!0},getOwnPropertyDescriptor(i,c){const r=Dl(i),s=Reflect.getOwnPropertyDescriptor(r,c);return s&&{writable:!0,configurable:i.type_!==1||c!=="length",enumerable:s.enumerable,value:r[c]}},defineProperty(){ht(11)},getPrototypeOf(i){return Ta(i.base_)},setPrototypeOf(){ht(12)}},An={};$u(Us,(i,c)=>{An[i]=function(){return arguments[0]=arguments[0][0],c.apply(this,arguments)}});An.deleteProperty=function(i,c){return An.set.call(this,i,c,void 0)};An.set=function(i,c,r){return Us.set.call(this,i[0],c,r,i[0])};function Ts(i,c){const r=i[We];return(r?Dl(r):i)[c]}function Km(i,c,r){var h;const s=Fd(c,r);return s?"value"in s?s.value:(h=s.get)==null?void 0:h.call(i.draft_):void 0}function Fd(i,c){if(!(c in i))return;let r=Ta(i);for(;r;){const s=Object.getOwnPropertyDescriptor(r,c);if(s)return s;r=Ta(r)}}function Ds(i){i.modified_||(i.modified_=!0,i.parent_&&Ds(i.parent_))}function Ms(i){i.copy_||(i.copy_=zs(i.base_,i.scope_.immer_.useStrictShallowCopy_))}var Jm=class{constructor(i){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(c,r,s)=>{if(typeof c=="function"&&typeof r!="function"){const g=r;r=c;const _=this;return function(O=g,...D){return _.produce(O,A=>r.call(this,A,...D))}}typeof r!="function"&&ht(6),s!==void 0&&typeof s!="function"&&ht(7);let h;if(Rl(c)){const g=Ld(this),_=Os(c,void 0);let z=!0;try{h=r(_),z=!1}finally{z?As(g):Es(g)}return qd(g,s),Yd(h,g)}else if(!c||typeof c!="object"){if(h=r(c),h===void 0&&(h=c),h===Kd&&(h=void 0),this.autoFreeze_&&Rs(h,!0),s){const g=[],_=[];Ul("Patches").generateReplacementPatches_(c,h,g,_),s(g,_)}return h}else ht(1,c)},this.produceWithPatches=(c,r)=>{if(typeof c=="function")return(_,...z)=>this.produceWithPatches(_,O=>c(O,...z));let s,h;return[this.produce(c,r,(_,z)=>{s=_,h=z}),s,h]},typeof(i==null?void 0:i.autoFreeze)=="boolean"&&this.setAutoFreeze(i.autoFreeze),typeof(i==null?void 0:i.useStrictShallowCopy)=="boolean"&&this.setUseStrictShallowCopy(i.useStrictShallowCopy)}createDraft(i){Rl(i)||ht(8),Ma(i)&&(i=Wm(i));const c=Ld(this),r=Os(i,void 0);return r[We].isManual_=!0,Es(c),r}finishDraft(i,c){const r=i&&i[We];(!r||!r.isManual_)&&ht(9);const{scope_:s}=r;return qd(s,c),Yd(void 0,s)}setAutoFreeze(i){this.autoFreeze_=i}setUseStrictShallowCopy(i){this.useStrictShallowCopy_=i}applyPatches(i,c){let r;for(r=c.length-1;r>=0;r--){const h=c[r];if(h.path.length===0&&h.op==="replace"){i=h.value;break}}r>-1&&(c=c.slice(r+1));const s=Ul("Patches").applyPatches_;return Ma(i)?s(i,c):this.produce(i,h=>s(h,c))}};function Os(i,c){const r=ei(i)?Ul("MapSet").proxyMap_(i,c):ti(i)?Ul("MapSet").proxySet_(i,c):Vm(i,c);return(c?c.scope_:$d()).drafts_.push(r),r}function Wm(i){return Ma(i)||ht(10,i),Id(i)}function Id(i){if(!Rl(i)||li(i))return i;const c=i[We];let r;if(c){if(!c.modified_)return c.base_;c.finalized_=!0,r=zs(i,c.scope_.immer_.useStrictShallowCopy_)}else r=zs(i,!0);return $u(r,(s,h)=>{Wd(r,s,Id(h))}),c&&(c.finalized_=!1),r}var $e=new Jm,$m=$e.produce;$e.produceWithPatches.bind($e);$e.setAutoFreeze.bind($e);$e.setUseStrictShallowCopy.bind($e);$e.applyPatches.bind($e);$e.createDraft.bind($e);$e.finishDraft.bind($e);const Fm=i=>(c,r,s)=>(s.setState=(h,g,..._)=>{const z=typeof h=="function"?$m(h):h;return c(z,g,..._)},i(s.setState,r,s)),Pd=Fm;class Wu extends Error{constructor(c,r,s,h){super(`API Error: ${c} ${r}`),this.status=c,this.statusText=r,this.code=s,this.details=h,this.name="APIError"}}class Im extends Error{constructor(c){super(c),this.name="NetworkError"}}class Pm extends Error{constructor(c){super(c),this.name="TimeoutError"}}class Cs{constructor(c={}){st(this,"config");st(this,"requestInterceptors",[]);st(this,"responseInterceptors",[]);st(this,"errorInterceptors",[]);this.config={baseURL:"http://localhost:8089",timeout:1e4,retryAttempts:3,retryDelay:1e3,...c}}addRequestInterceptor(c){this.requestInterceptors.push(c)}addResponseInterceptor(c){this.responseInterceptors.push(c)}addErrorInterceptor(c){this.errorInterceptors.push(c)}async get(c,r){return this.request({url:c,method:"GET",params:r})}async post(c,r){return this.request({url:c,method:"POST",data:r})}async put(c,r){return this.request({url:c,method:"PUT",data:r})}async delete(c){return this.request({url:c,method:"DELETE"})}async request(c){return this.retry(()=>this.executeRequest(c),this.config.retryAttempts)}async executeRequest(c){let r=c;for(const g of this.requestInterceptors)r=g(r);const s=new AbortController,h=setTimeout(()=>s.abort(),this.config.timeout);try{const g=this.buildURL(r.url,r.params),_=await fetch(g,{method:r.method,headers:{"Content-Type":"application/json",...r.headers},body:r.data?JSON.stringify(r.data):void 0,signal:s.signal});if(clearTimeout(h),!_.ok)throw new Wu(_.status,_.statusText);let z=await _.json();for(const O of this.responseInterceptors)z=O(z);return z}catch(g){clearTimeout(h);for(const _ of this.errorInterceptors)await _(g);throw this.normalizeError(g)}}async retry(c,r){try{return await c()}catch(s){if(r>0&&this.shouldRetry(s))return await this.delay(this.config.retryDelay),this.retry(c,r-1);throw s}}shouldRetry(c){return c.name==="NetworkError"||c.name==="TimeoutError"||c instanceof Wu&&c.status>=500&&c.status<600}delay(c){return new Promise(r=>setTimeout(r,c))}buildURL(c,r){const s=new URL(c,this.config.baseURL);return r&&Object.entries(r).forEach(([h,g])=>{g!=null&&s.searchParams.append(h,String(g))}),s.toString()}normalizeError(c){return c.name==="AbortError"?new Pm("Request timeout"):c instanceof TypeError&&c.message.includes("fetch")?new Im("Network connection failed"):c instanceof Wu?c:new Error(c.message||"Unknown error occurred")}}const Ol=new Cs;Ol.addRequestInterceptor(i=>{const c=localStorage.getItem("auth_token");return c&&(i.headers={...i.headers,Authorization:`Bearer ${c}`}),i.headers={...i.headers,"X-Request-ID":e1()},i});Ol.addResponseInterceptor(i=>i);Ol.addErrorInterceptor(async i=>{throw i instanceof Wu&&i.status===401&&(localStorage.removeItem("auth_token"),console.warn("Authentication failed, please login again")),console.error("API Error:",i),i});function e1(){return`req_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}class t1 extends Cs{async getTasks(c){const r={};return c!=null&&c.limit&&(r.limit=c.limit),c!=null&&c.offset&&(r.offset=c.offset),c!=null&&c.search&&(r.search=c.search),c!=null&&c.dateRange&&(r.start_date=c.dateRange[0],r.end_date=c.dateRange[1]),this.get("/api/tasks",r)}async getTask(c){return this.get(`/api/tasks/${c}`)}async createTask(c){return{id:this.generateTaskId(),title:c,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),message_count:0,report_file_path:null,report_html_filename:"report.html"}}async updateTask(c,r){return this.put(`/api/tasks/${c}`,r)}async deleteTask(c){return this.delete(`/api/tasks/${c}`)}async deleteMultipleTasks(c){return this.post("/api/tasks/batch-delete",{task_ids:c})}async checkTaskExists(c){try{return await this.getTask(c),!0}catch{return!1}}async searchTasks(c,r=20){return this.getTasks({search:c,limit:r})}async getRecentTasks(c=10){return this.getTasks({limit:c})}generateTaskId(){const c=Date.now(),r=Math.random().toString(36).substr(2,9);return`task_${c}_${r}`}}const ja=new t1;class l1 extends Cs{async getConfig(){return this.get("/api/config")}async updateConfig(c){return this.put("/api/config",c)}async validateLLMConfig(c){try{return!(!this.isValidURL(c.llm_base_url)||!c.llm_api_key||c.llm_api_key.length<10||!c.llm_model||c.llm_model.trim().length===0)}catch(r){return console.error("LLM config validation failed:",r),!1}}async testLLMConnection(c){const r=c||{};return this.post("/api/config/test-llm",r)}async resetConfig(){return this.post("/api/config/reset")}async exportConfig(){return(await this.getConfig()).data}async importConfig(c){return this.updateConfig(c)}async getConfigHistory(){return this.get("/api/config/history")}async restoreConfig(c){return this.post(`/api/config/restore/${c}`)}isValidURL(c){try{return new URL(c),!0}catch{return!1}}validateConfig(c){const r=[];return c.llm_base_url!==void 0&&(c.llm_base_url?this.isValidURL(c.llm_base_url)||r.push("LLM Base URL format is invalid"):r.push("LLM Base URL is required")),c.llm_api_key!==void 0&&(c.llm_api_key?c.llm_api_key.length<10&&r.push("LLM API Key is too short"):r.push("LLM API Key is required")),c.llm_model!==void 0&&(!c.llm_model||c.llm_model.trim().length===0)&&r.push("LLM Model is required"),c.report_storage_path!==void 0&&(c.report_storage_path||r.push("Report storage path is required")),{isValid:r.length===0,errors:r}}getDefaultConfig(){return{llm_base_url:"http://localhost:8088/v1/chat/completions",llm_api_key:"",llm_model:"claude-3-sonnet",report_storage_path:"./reports",debug_mode:!1}}}new l1;class a1{constructor(){st(this,"ws",null);st(this,"reconnectAttempts",0);st(this,"maxReconnectAttempts",5);st(this,"reconnectDelay",1e3);st(this,"messageQueue",[]);st(this,"currentTaskId",null);st(this,"listeners",{message:[],connectionChange:[],error:[]})}connect(c){return new Promise((r,s)=>{this.currentTaskId=c;const h=this.getWebSocketURL(c);console.log(`Connecting to WebSocket: ${h}`),this.ws=new WebSocket(h),this.notifyConnectionChange("connecting"),this.ws.onopen=()=>{console.log("WebSocket connected successfully"),this.reconnectAttempts=0,this.notifyConnectionChange("connected"),this.flushMessageQueue(),r()},this.ws.onmessage=g=>{try{const _=JSON.parse(g.data);this.notifyMessage(_)}catch(_){console.error("Failed to parse WebSocket message:",_)}},this.ws.onclose=g=>{console.log("WebSocket connection closed:",g.code,g.reason),this.notifyConnectionChange("disconnected"),g.code!==1e3&&this.currentTaskId&&this.handleReconnect()},this.ws.onerror=g=>{console.error("WebSocket error:",g),this.notifyConnectionChange("error"),this.notifyError(g),s(g)}})}disconnect(){this.ws&&(this.ws.close(1e3,"Client disconnect"),this.ws=null),this.currentTaskId=null,this.messageQueue=[],this.reconnectAttempts=0}sendMessage(c){this.ws&&this.ws.readyState===WebSocket.OPEN?(console.log("Sending WebSocket message:",c),this.ws.send(JSON.stringify(c))):(console.log("WebSocket not ready, queuing message:",c),this.messageQueue.push(c))}onMessage(c){return this.listeners.message.push(c),()=>{const r=this.listeners.message.indexOf(c);r>-1&&this.listeners.message.splice(r,1)}}onConnectionChange(c){return this.listeners.connectionChange.push(c),()=>{const r=this.listeners.connectionChange.indexOf(c);r>-1&&this.listeners.connectionChange.splice(r,1)}}onError(c){return this.listeners.error.push(c),()=>{const r=this.listeners.error.indexOf(c);r>-1&&this.listeners.error.splice(r,1)}}getConnectionStatus(){if(!this.ws)return"disconnected";switch(this.ws.readyState){case WebSocket.CONNECTING:return"connecting";case WebSocket.OPEN:return"connected";case WebSocket.CLOSING:case WebSocket.CLOSED:return"disconnected";default:return"error"}}reconnect(){return this.currentTaskId?(this.disconnect(),this.connect(this.currentTaskId)):Promise.reject(new Error("No task ID available for reconnection"))}handleReconnect(){if(this.reconnectAttempts<this.maxReconnectAttempts&&this.currentTaskId){this.reconnectAttempts++;const c=this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1);console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${c}ms`),setTimeout(()=>{this.currentTaskId&&this.connect(this.currentTaskId).catch(r=>{console.error("Reconnection failed:",r)})},c)}else console.error("Max reconnection attempts reached"),this.notifyConnectionChange("error")}flushMessageQueue(){for(;this.messageQueue.length>0;){const c=this.messageQueue.shift();c&&this.sendMessage(c)}}notifyMessage(c){this.listeners.message.forEach(r=>{try{r(c)}catch(s){console.error("Error in message callback:",s)}})}notifyConnectionChange(c){this.listeners.connectionChange.forEach(r=>{try{r(c)}catch(s){console.error("Error in connection change callback:",s)}})}notifyError(c){this.listeners.error.forEach(r=>{try{r(c)}catch(s){console.error("Error in error callback:",s)}})}getWebSocketURL(c){return`${window.location.protocol==="https:"?"wss:":"ws:"}//localhost:8088/ws/chat/${c}`}destroy(){this.disconnect(),this.listeners.message=[],this.listeners.connectionChange=[],this.listeners.error=[]}}const El=new a1,ws=Zd()(Vd(Lm(Pd((i,c)=>({tasks:[],currentTask:null,loading:!1,error:null,pagination:{page:1,limit:20,total:0},filters:{search:""},selectedTaskIds:[],fetchTasks:async r=>{i(s=>{s.loading=!0,s.error=null});try{const s=await ja.getTasks({...c().pagination,...c().filters,...r});i(h=>{s.success&&s.data&&(h.tasks=s.data,h.pagination.total=s.data.length),h.loading=!1})}catch(s){i(h=>{h.error=s instanceof Error?s.message:"Failed to fetch tasks",h.loading=!1})}},selectTask:async r=>{i(s=>{s.loading=!0,s.error=null});try{const s=await ja.getTask(r);return i(h=>{s.success&&s.data&&(h.currentTask=s.data.task),h.loading=!1}),s.data}catch(s){throw i(h=>{h.error=s instanceof Error?s.message:"Failed to select task",h.loading=!1}),s}},createTask:async r=>{try{const s=await ja.createTask(r);return i(h=>{h.tasks.unshift(s),h.currentTask=s}),s}catch(s){throw i(h=>{h.error=s instanceof Error?s.message:"Failed to create task"}),s}},updateTask:async(r,s)=>{try{const h=await ja.updateTask(r,s);i(g=>{var _;if(h.success&&h.data){const z=g.tasks.findIndex(O=>O.id===r);z!==-1&&(g.tasks[z]=h.data),((_=g.currentTask)==null?void 0:_.id)===r&&(g.currentTask=h.data)}})}catch(h){throw i(g=>{g.error=h instanceof Error?h.message:"Failed to update task"}),h}},deleteTask:async r=>{try{await ja.deleteTask(r),i(s=>{var h;s.tasks=s.tasks.filter(g=>g.id!==r),((h=s.currentTask)==null?void 0:h.id)===r&&(s.currentTask=null)})}catch(s){throw i(h=>{h.error=s instanceof Error?s.message:"Failed to delete task"}),s}},selectMultipleTasks:r=>{i(s=>{s.selectedTaskIds=r})},deleteMultipleTasks:async r=>{try{await ja.deleteMultipleTasks(r),i(s=>{s.tasks=s.tasks.filter(h=>!r.includes(h.id)),s.selectedTaskIds=[],s.currentTask&&r.includes(s.currentTask.id)&&(s.currentTask=null)})}catch(s){throw i(h=>{h.error=s instanceof Error?s.message:"Failed to delete tasks"}),s}},setSearch:r=>{i(s=>{s.filters.search=r,s.pagination.page=1}),c().fetchTasks()},setFilters:r=>{i(s=>{s.filters={...s.filters,...r},s.pagination.page=1}),c().fetchTasks()},clearFilters:()=>{i(r=>{r.filters={search:""},r.pagination.page=1}),c().fetchTasks()},reset:()=>{i(r=>{r.tasks=[],r.currentTask=null,r.loading=!1,r.error=null,r.selectedTaskIds=[],r.pagination={page:1,limit:20,total:0},r.filters={search:""}})}})),{name:"task-store",partialize:i=>({currentTask:i.currentTask,filters:i.filters})}),{name:"TaskStore"})),n1={getTaskMessages:async i=>{var c;try{console.log(`Fetching messages for task: ${i}`);const r=await Ol.get(`/api/tasks/${i}`);if(console.log("Raw API response:",r),(c=r==null?void 0:r.data)!=null&&c.messages&&Array.isArray(r.data.messages)){const s=r.data.messages;return console.log(`Successfully loaded ${s.length} messages:`,s),s}return console.log("No messages found in response.data.messages, returning empty array"),[]}catch(r){return console.error("Failed to fetch task messages:",r),[]}},sendMessage:async(i,c,r)=>(await Ol.post(`/api/tasks/${i}/messages`,{content:c,context_html:r})).data,deleteMessage:async(i,c)=>{await Ol.delete(`/api/tasks/${i}/messages/${c}`)},regenerateMessage:async(i,c)=>(await Ol.post(`/api/tasks/${i}/messages/${c}/regenerate`)).data},Qd=()=>`msg_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;let Xd=!1;const u1=Zd()(Vd(Pd((i,c)=>(Xd||(Xd=!0,El.onMessage(r=>{const s=c();switch(r.type){case"assistant_message_chunk":if(s.currentResponseId)s.appendResponseChunk(s.currentResponseId,r.content);else{const h=Qd();s.startResponse(h),s.appendResponseChunk(h,r.content)}break;case"code_snippet_start":break;case"code_snippet_chunk":break;case"code_snippet_end":s.currentResponseId&&s.finishResponse(s.currentResponseId);break;case"error":i(h=>{h.error=r.content,h.isTyping=!1,h.isReceivingResponse=!1});break;default:console.warn("Unknown WebSocket message type:",r)}}),El.onConnectionChange(r=>{i(s=>{s.connectionStatus=r,s.isConnected=r==="connected"})})),{messages:[],isConnected:!1,connectionStatus:"disconnected",isTyping:!1,inputContent:"",selectedContext:null,isReceivingResponse:!1,currentResponseId:null,error:null,sendMessage:(r,s)=>{const h={id:Qd(),role:"user",content:r,timestamp:new Date().toISOString(),has_code_snippet:!1};i(g=>{g.messages.push(h),g.inputContent="",g.selectedContext=null,g.isTyping=!0}),El.sendMessage({type:"user_message",content:r,context_html:s||""})},addMessage:r=>{i(s=>{s.messages.push(r)})},updateMessage:(r,s)=>{i(h=>{const g=h.messages.findIndex(_=>_.id===r);g!==-1&&(h.messages[g]={...h.messages[g],...s})})},deleteMessage:r=>{i(s=>{s.messages=s.messages.filter(h=>h.id!==r)})},connect:async r=>{console.log(`[ChatStore] Connecting to task: ${r}`),i(s=>{console.log(`[ChatStore] Clearing previous messages, had ${s.messages.length} messages`),s.messages=[],s.connectionStatus="connecting",s.error=null,s.isTyping=!1,s.isReceivingResponse=!1,s.currentResponseId=null,s.selectedContext=null}),El.disconnect();try{console.log(`[ChatStore] Loading task messages for: ${r}`),await c().loadTaskMessages(r);const s=c().messages;console.log(`[ChatStore] After loading messages, store has ${s.length} messages`),console.log(`[ChatStore] Connecting WebSocket for: ${r}`),await El.connect(r),i(h=>{h.isConnected=!0,h.connectionStatus="connected"}),console.log(`[ChatStore] Successfully connected to task: ${r}`)}catch(s){console.error(`[ChatStore] Failed to connect to task ${r}:`,s),i(h=>{h.isConnected=!1,h.connectionStatus="error",h.error=s instanceof Error?s.message:"Connection failed"})}},loadTaskMessages:async r=>{try{console.log(`Loading messages for task: ${r}`);const s=await n1.getTaskMessages(r);console.log(`Loaded ${s.length} messages for task: ${r}`,s);const h=s.sort((g,_)=>new Date(g.timestamp).getTime()-new Date(_.timestamp).getTime());if(i(g=>{g.messages=h}),console.log(`Set ${h.length} messages in store`),h.length===0){const g={id:`welcome_${r}`,role:"system",content:`欢迎！您已连接到任务: ${r}。请开始对话。`,timestamp:new Date().toISOString(),has_code_snippet:!1};i(_=>{_.messages=[g]}),console.log("Added welcome message")}}catch(s){console.error("Failed to load task messages:",s);const h={id:`system_${r}`,role:"system",content:`连接失败，请重试。任务ID: ${r}`,timestamp:new Date().toISOString(),has_code_snippet:!1};i(g=>{g.messages=[h]}),console.log("Added error message")}},disconnect:()=>{El.disconnect(),i(r=>{r.isConnected=!1,r.connectionStatus="disconnected"})},clearMessages:()=>{i(r=>{r.messages=[],r.isTyping=!1,r.isReceivingResponse=!1,r.currentResponseId=null,r.selectedContext=null,r.error=null})},reconnect:()=>{El.reconnect().catch(r=>{i(s=>{s.error=r.message})})},setInputContent:r=>{i(s=>{s.inputContent=r})},setSelectedContext:r=>{i(s=>{s.selectedContext=r})},startResponse:r=>{i(h=>{h.isReceivingResponse=!0,h.currentResponseId=r,h.isTyping=!1});const s={id:r,role:"assistant",content:"",timestamp:new Date().toISOString(),has_code_snippet:!1};c().addMessage(s)},appendResponseChunk:(r,s)=>{i(h=>{const g=h.messages.findIndex(_=>_.id===r);g!==-1&&(h.messages[g].content+=s)})},finishResponse:r=>{i(s=>{s.isReceivingResponse=!1,s.currentResponseId=null;const h=s.messages.findIndex(g=>g.id===r);h!==-1&&(s.messages[h].has_code_snippet=!0)})},loadMessages:r=>{i(s=>{s.messages=r})},reset:()=>{i(r=>{r.messages=[],r.inputContent="",r.selectedContext=null,r.isReceivingResponse=!1,r.currentResponseId=null,r.error=null}),c().disconnect()}})),{name:"ChatStore"})),i1=({task:i,isActive:c=!1,onClick:r,onDelete:s,className:h=""})=>{const[g,_]=re.useState(!1),z=()=>{r(i.id)},O=A=>{A.stopPropagation(),s&&s(i.id)},D=A=>{const K=new Date(A),Q=(new Date().getTime()-K.getTime())/(1e3*60*60);return Q<1?"刚刚":Q<24?`${Math.floor(Q)}小时前`:Q<168?`${Math.floor(Q/24)}天前`:K.toLocaleDateString()};return d.jsxs("div",{className:q("group relative p-3 rounded-lg cursor-pointer task-item",c?"bg-primary-50 border-l-4 border-primary-600 shadow-sm":"hover:bg-gray-50 border-l-4 border-transparent",h),onClick:z,onMouseEnter:()=>_(!0),onMouseLeave:()=>_(!1),children:[d.jsxs("div",{className:"flex items-start justify-between",children:[d.jsx("h3",{className:q("font-medium text-sm leading-5 truncate pr-2",c?"text-primary-900":"text-gray-900"),title:i.title,children:i.title}),s&&(g||c)&&d.jsx("button",{onClick:O,className:"flex-shrink-0 p-1 rounded hover:bg-red-100 text-gray-400 hover:text-red-600 transition-colors",title:"删除任务",children:d.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]}),d.jsxs("div",{className:"mt-2 flex items-center justify-between text-xs",children:[d.jsxs("div",{className:"flex items-center space-x-3",children:[d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("svg",{className:"w-3 h-3 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),d.jsx("span",{className:q(c?"text-primary-600":"text-gray-500"),children:i.message_count})]}),i.report_file_path&&d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("svg",{className:"w-3 h-3 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),d.jsx("span",{className:"text-green-600",children:"已生成"})]})]}),d.jsx("span",{className:q(c?"text-primary-500":"text-gray-400"),children:D(i.updated_at)})]}),c&&d.jsx("div",{className:"absolute right-2 top-1/2 transform -translate-y-1/2",children:d.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full"})})]})},c1=({tasks:i,currentTaskId:c,onTaskSelect:r,className:s=""})=>{const h=re.useRef(null),g=re.useCallback(_=>{var O;const z=((O=h.current)==null?void 0:O.scrollTop)||0;r(_),setTimeout(()=>{h.current&&(h.current.scrollTop=z)},0)},[r]);return i.length===0?d.jsx("div",{className:q("flex flex-col items-center justify-center h-full p-8",s),children:d.jsxs("div",{className:"text-center",children:[d.jsx("svg",{className:"w-12 h-12 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),d.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-1",children:"暂无任务"}),d.jsx("p",{className:"text-sm text-gray-500",children:"点击上方按钮创建第一个任务"})]})}):d.jsx("div",{ref:h,className:q("h-full overflow-y-auto custom-scrollbar",s),children:d.jsx("div",{className:"p-2 space-y-1",children:i.map(_=>d.jsx(i1,{task:_,isActive:_.id===c,onClick:g},_.id))})})},s1=({value:i="",placeholder:c="搜索任务...",onSearch:r,onClear:s,className:h=""})=>{const[g,_]=re.useState(i),z=A=>{const K=A.target.value;_(K),r(K)},O=()=>{_(""),r(""),s&&s()},D=A=>{A.key==="Escape"&&O()};return d.jsxs("div",{className:q("relative",h),children:[d.jsxs("div",{className:"relative",children:[d.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:d.jsx("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),d.jsx("input",{type:"text",value:g,onChange:z,onKeyDown:D,placeholder:c,className:q("w-full pl-10 pr-10 py-2 text-sm","border border-gray-300 rounded-lg","bg-white placeholder-gray-500","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent","transition-colors duration-200")}),g&&d.jsx("button",{onClick:O,className:"absolute inset-y-0 right-0 pr-3 flex items-center hover:text-gray-600 transition-colors",children:d.jsx("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),g&&d.jsx("div",{className:"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-48 overflow-y-auto"})]})},f1=({onClick:i,loading:c=!1,className:r=""})=>{const[s,h]=re.useState(!1);return d.jsx("button",{onClick:i,disabled:c,className:q("w-full flex items-center justify-center space-x-2 p-3 rounded-lg","bg-gradient-to-r from-primary-600 to-blue-600 text-white font-medium text-sm shadow-lg","hover:from-primary-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2","btn-hover","disabled:opacity-50 disabled:cursor-not-allowed",r),onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:c?d.jsxs(d.Fragment,{children:[d.jsxs("svg",{className:"animate-spin w-4 h-4",fill:"none",viewBox:"0 0 24 24",children:[d.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),d.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d.jsx("span",{children:"创建中..."})]}):d.jsxs(d.Fragment,{children:[d.jsx("svg",{className:q("w-4 h-4 transition-transform",{"scale-110":s}),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),d.jsx("span",{children:"新建任务"})]})})},r1=({message:i="加载中...",size:c="md",variant:r="spinner",className:s=""})=>{const h={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"},g=()=>d.jsxs("svg",{className:q("animate-spin",h[c]),fill:"none",viewBox:"0 0 24 24",children:[d.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),d.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),_=()=>d.jsx("div",{className:"flex space-x-1",children:[0,1,2].map(D=>d.jsx("div",{className:q("bg-current rounded-full animate-pulse",c==="sm"?"w-1 h-1":c==="md"?"w-2 h-2":"w-3 h-3"),style:{animationDelay:`${D*.2}s`,animationDuration:"1s"}},D))}),z=()=>d.jsx("div",{className:q("bg-current rounded-full animate-pulse-slow",h[c])}),O=()=>{switch(r){case"dots":return _();case"pulse":return z();default:return g()}};return d.jsxs("div",{className:q("flex flex-col items-center justify-center space-y-3",s),children:[d.jsx("div",{className:"text-primary-600",children:O()}),i&&d.jsx("div",{className:"text-sm text-gray-600 text-center animate-pulse",children:i})]})},o1=({collapsed:i=!1,className:c=""})=>{const{tasks:r,currentTask:s,loading:h,error:g,fetchTasks:_,selectTask:z,createTask:O,setSearch:D}=ws(),[A,K]=re.useState("");re.useEffect(()=>{_()},[_]);const G=async w=>{try{await z(w)}catch(J){console.error("Failed to select task:",J)}},Q=async()=>{try{const w=`新任务 - ${new Date().toLocaleString()}`;await O(w)}catch(w){console.error("Failed to create task:",w)}},oe=w=>{K(w),D(w)},C=()=>{K(""),D("")};return i?d.jsxs("div",{className:q("flex flex-col items-center py-4 space-y-4",c),children:[d.jsx("button",{onClick:Q,className:"p-3 rounded-lg bg-primary-600 text-white hover:bg-primary-700 transition-colors",title:"新建任务",children:d.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})})}),d.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[r.slice(0,3).map(w=>d.jsx("button",{onClick:()=>G(w.id),className:q("w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium transition-colors",(s==null?void 0:s.id)===w.id?"bg-primary-100 text-primary-700 border-2 border-primary-600":"bg-gray-100 text-gray-600 hover:bg-gray-200"),title:w.title,children:w.title.charAt(0).toUpperCase()},w.id)),r.length>3&&d.jsxs("div",{className:"w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center text-xs text-gray-500",children:["+",r.length-3]})]})]}):d.jsxs("div",{className:q("flex flex-col h-full",c),children:[d.jsxs("div",{className:"p-4 space-y-3",children:[d.jsx(s1,{value:A,onSearch:oe,onClear:C,placeholder:"搜索任务..."}),d.jsx(f1,{onClick:Q})]}),d.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:h?d.jsx("div",{className:"flex items-center justify-center h-32",children:d.jsx(r1,{message:"加载任务中...",size:"sm"})}):g?d.jsx("div",{className:"p-4",children:d.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:[d.jsx("p",{className:"text-sm text-red-800",children:g}),d.jsx("button",{onClick:()=>_(),className:"mt-2 text-sm text-red-600 hover:text-red-800 underline",children:"重试"})]})}):d.jsx(c1,{tasks:r,currentTaskId:s==null?void 0:s.id,onTaskSelect:G})}),d.jsx("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:d.jsxs("div",{className:"text-xs text-gray-500 text-center",children:["共 ",r.length," 个任务"]})})]})},d1=({className:i=""})=>{const[c,r]=re.useState(!1),s=()=>{r(!c)};return d.jsxs("div",{className:q("bg-gray-50",i),children:[d.jsxs("button",{onClick:s,className:"w-full flex items-center justify-between p-4 hover:bg-gray-100 transition-colors",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsxs("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),d.jsx("span",{className:"text-sm font-medium text-gray-700",children:"系统设置"})]}),d.jsx("svg",{className:q("w-4 h-4 text-gray-500 transition-transform",{"rotate-180":c}),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),c&&d.jsxs("div",{className:"px-4 pb-4 space-y-3",children:[d.jsxs("div",{className:"space-y-2",children:[d.jsx("label",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:"LLM 配置"}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"API 设置"}),d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"模型配置"})]})]}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("label",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:"界面设置"}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"主题设置"}),d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"语言设置"})]})]}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("label",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:"其他"}),d.jsxs("div",{className:"space-y-2",children:[d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"导出数据"}),d.jsx("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:"关于"})]})]})]})]})},h1=({collapsed:i=!1,onToggle:c,isMobile:r=!1,className:s=""})=>d.jsxs("div",{className:q("flex flex-col h-full",s),children:[d.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-primary-50 to-blue-50",children:[!i&&d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:d.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),d.jsx("h1",{className:"text-xl font-bold bg-gradient-to-r from-primary-600 to-blue-600 bg-clip-text text-transparent",children:"BestReport"})]}),!r&&c&&d.jsx("button",{onClick:c,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",title:i?"展开侧边栏":"收起侧边栏",children:d.jsx("svg",{className:q("w-5 h-5 text-gray-600 transition-transform",{"rotate-180":i}),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})})]}),d.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[d.jsx("div",{className:"flex-1 overflow-hidden",children:d.jsx(o1,{collapsed:i})}),!i&&d.jsx("div",{className:"border-t border-gray-200",children:d.jsx(d1,{})})]}),!i&&d.jsx("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:d.jsx("div",{className:"text-xs text-gray-500 text-center",children:"BestReport v1.0.0"})})]}),m1=({className:i=""})=>{const{currentTask:c}=ws(),[r,s]=re.useState(""),[h,g]=re.useState("");re.useEffect(()=>{var D;if(s(""),(D=window.getSelection())==null||D.removeAllRanges(),c!=null&&c.report_file_path){const A=_(c.id,c.title);g(A)}else g("")},[c==null?void 0:c.id,c==null?void 0:c.report_file_path,c==null?void 0:c.title]);const _=(D,A)=>`
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
          ${A} - 数据分析报告
        </h1>

        <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #374151; margin-top: 0;">任务ID: ${D}</h2>
          <p style="line-height: 1.6; color: #4b5563;">
            本报告基于任务 "${A}" 的数据分析结果，展示了关键业务指标的趋势和洞察。
            每个任务都有其独特的分析内容和结论。
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">关键发现</h2>
        <ul style="line-height: 1.8; color: #4b5563;">
          <li>任务 ${D} 的数据处理已完成</li>
          <li>分析结果显示良好的数据质量</li>
          <li>建议继续监控相关指标</li>
          <li>后续可进行深度分析</li>
        </ul>

        <div style="background: #ecfdf5; border-left: 4px solid #10b981; padding: 15px; margin: 20px 0;">
          <h3 style="color: #065f46; margin-top: 0;">任务状态</h3>
          <p style="color: #047857; margin-bottom: 0;">
            任务 "${A}" 已成功完成分析，报告已生成。
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">数据可视化</h2>
        <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
          <div style="width: 100%; height: 200px; background: linear-gradient(45deg, #3b82f6, #10b981); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
            📊 ${A} 数据图表
          </div>
          <p style="margin-top: 10px; color: #6b7280; font-size: 14px;">
            任务 ${D} 的数据可视化展示
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">结论与建议</h2>
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;">
          <ol style="line-height: 1.8; color: #92400e; margin: 0;">
            <li>任务 "${A}" 执行成功</li>
            <li>数据分析结果符合预期</li>
            <li>建议定期更新分析内容</li>
            <li>可考虑扩展分析维度</li>
          </ol>
        </div>
      </div>
    `,z=()=>{const D=window.getSelection();D&&D.toString().trim()&&(s(D.toString().trim()),console.log("Selected text:",D.toString().trim()))},O=()=>{var D;s(""),(D=window.getSelection())==null||D.removeAllRanges()};return c?d.jsxs("div",{className:q("flex flex-col h-full bg-white",i),children:[r&&d.jsx("div",{className:"bg-blue-50 border-b border-blue-200 p-3",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),d.jsxs("span",{className:"text-sm font-medium text-blue-800",children:['已选择文本: "',r.substring(0,50),r.length>50?"...":"",'"']})]}),d.jsx("button",{onClick:O,className:"text-blue-600 hover:text-blue-800 text-sm",children:"清除选择"})]})}),d.jsx("div",{className:"flex-1 overflow-auto",style:{display:"grid",placeItems:"start center",padding:"24px",width:"100%"},children:c.report_file_path&&h?d.jsx("div",{style:{width:"100%",maxWidth:"800px",margin:"0 auto"},children:d.jsx("div",{className:"prose prose-lg",style:{maxWidth:"none",width:"100%"},onMouseUp:z,dangerouslySetInnerHTML:{__html:h}})}):d.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[d.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4",children:d.jsx("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"报告生成中"}),d.jsx("p",{className:"text-gray-600 max-w-md",children:"当前任务还没有生成报告。请在右侧聊天区域描述您的需求，AI将为您生成详细的数据分析报告。"})]})})]}):d.jsx("div",{className:q("flex flex-col items-center justify-center h-full bg-gray-50",i),children:d.jsxs("div",{className:"text-center max-w-md",children:[d.jsx("div",{className:"w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6",children:d.jsx("svg",{className:"w-10 h-10 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),d.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"选择任务查看报告"}),d.jsx("p",{className:"text-gray-600 leading-relaxed",children:"请从左侧任务列表中选择一个任务，或创建新任务开始生成报告。 生成的报告将在此处显示，您可以选择报告中的内容与AI进行讨论。"})]})})},y1=({className:i=""})=>d.jsxs("div",{className:q("flex flex-col h-full bg-white",i),children:[d.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50",children:[d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-lg",children:d.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),d.jsx("h2",{className:"text-lg font-semibold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent",children:"报告查看器"})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),d.jsx("span",{className:"text-sm text-gray-600",children:"就绪"})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-200 transition-colors",children:d.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),d.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-200 transition-colors",children:d.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})})})]})]}),d.jsx("div",{className:"flex-1 overflow-hidden",children:d.jsx(m1,{})})]}),v1=({taskTitle:i,connectionStatus:c,className:r=""})=>{const s=()=>{switch(c){case"connected":return"bg-green-500";case"connecting":return"bg-yellow-500 animate-pulse";case"disconnected":return"bg-gray-400";case"error":return"bg-red-500";default:return"bg-gray-400"}},h=()=>{switch(c){case"connected":return"已连接";case"connecting":return"连接中";case"disconnected":return"未连接";case"error":return"连接错误";default:return"未知状态"}};return d.jsxs("div",{className:q("flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-blue-50",r),children:[d.jsxs("div",{className:"flex items-center space-x-3 min-w-0 flex-1",children:[d.jsx("div",{className:"flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg",children:d.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),d.jsxs("div",{className:"min-w-0 flex-1",children:[d.jsx("h2",{className:"text-sm font-semibold text-gray-900 truncate",title:i,children:i}),d.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[d.jsx("div",{className:q("w-2 h-2 rounded-full",s())}),d.jsx("span",{className:"text-xs text-gray-500",children:h()})]})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",title:"清空聊天记录",children:d.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})}),d.jsx("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors",title:"更多选项",children:d.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})})})]})]})},g1=({message:i,onRetry:c,className:r=""})=>{const[s,h]=re.useState(!1),g=A=>new Date(A).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),_=()=>{c&&c(i.id)},z=i.role==="user",O=i.role==="assistant",D=i.role==="system";return d.jsx("div",{className:q("flex",{"justify-end":z,"justify-start":O||D},r),onMouseEnter:()=>h(!0),onMouseLeave:()=>h(!1),children:d.jsxs("div",{className:q("flex space-x-2",{"flex-row-reverse space-x-reverse":z}),style:{maxWidth:"260px",width:"fit-content"},children:[d.jsx("div",{className:"flex-shrink-0",children:z?d.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center",children:d.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}):O?d.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:d.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}):d.jsx("div",{className:"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center",children:d.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),d.jsxs("div",{className:"flex flex-col space-y-1",children:[d.jsxs("div",{className:q("px-4 py-2 rounded-2xl",{"bg-primary-600 text-white":z,"bg-gray-100 text-gray-900":O,"bg-yellow-50 text-yellow-800 border border-yellow-200":D}),children:[d.jsx("div",{className:"text-sm leading-relaxed whitespace-pre-wrap",children:i.content}),i.has_code_snippet&&O&&d.jsxs("div",{className:"mt-2 flex items-center space-x-1 text-xs text-gray-500",children:[d.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})}),d.jsx("span",{children:"已生成报告"})]})]}),d.jsxs("div",{className:q("flex items-center space-x-2 text-xs text-gray-500",{"justify-end":z,"justify-start":O||D}),children:[d.jsx("span",{children:g(i.timestamp)}),s&&d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("button",{onClick:()=>navigator.clipboard.writeText(i.content),className:"p-1 rounded hover:bg-gray-200 transition-colors",title:"复制消息",children:d.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})}),O&&c&&d.jsx("button",{onClick:_,className:"p-1 rounded hover:bg-gray-200 transition-colors",title:"重新生成",children:d.jsx("svg",{className:"w-3 h-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})})]})]})]})]})})},x1=({className:i=""})=>d.jsx("div",{className:q("flex justify-start",i),children:d.jsxs("div",{className:"flex max-w-[80%] space-x-3",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:d.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})})}),d.jsx("div",{className:"bg-gray-100 rounded-2xl px-4 py-3",children:d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsxs("div",{className:"flex space-x-1",children:[d.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),d.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),d.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]}),d.jsx("span",{className:"text-sm text-gray-500 ml-2",children:"AI正在思考..."})]})})]})}),b1=({messages:i,loading:c=!1,onRetry:r,className:s=""})=>{const h=re.useRef(null),g=re.useRef(null),_=()=>{var z;(z=h.current)==null||z.scrollIntoView({behavior:"smooth"})};return re.useEffect(()=>{_()},[i]),i.length===0&&!c?d.jsx("div",{className:q("flex flex-col items-center justify-center h-full p-8",s),children:d.jsxs("div",{className:"text-center max-w-sm",children:[d.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx("svg",{className:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"开始对话"}),d.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:"我是您的AI助手，可以帮助您生成数据分析报告。请告诉我您需要什么样的报告，我会为您创建详细的HTML报告。"}),d.jsxs("div",{className:"mt-4 space-y-2 text-xs text-gray-500",children:[d.jsx("p",{children:"💡 您可以："}),d.jsxs("ul",{className:"space-y-1",children:[d.jsx("li",{children:"• 描述您的数据分析需求"}),d.jsx("li",{children:"• 选择报告中的特定区域进行讨论"}),d.jsx("li",{children:"• 要求修改或优化报告内容"})]})]})]})}):d.jsx("div",{ref:g,className:q("flex flex-col h-full overflow-y-auto custom-scrollbar",s),children:d.jsxs("div",{className:"flex-1 p-4 space-y-4",children:[i.map(z=>d.jsx(g1,{message:z,onRetry:r},z.id)),c&&d.jsx(x1,{}),d.jsx("div",{ref:h})]})})},p1=({onSend:i,disabled:c=!1,placeholder:r="输入消息...",selectedContext:s,onContextClear:h,className:g=""})=>{const[_,z]=re.useState(""),[O,D]=re.useState(!1),A=re.useRef(null),K=()=>{const w=A.current;w&&(w.style.height="auto",w.style.height=`${Math.min(w.scrollHeight,120)}px`)};re.useEffect(()=>{K()},[_]);const G=()=>{_.trim()&&!c&&(i(_.trim(),s||void 0),z(""))},Q=w=>{w.key==="Enter"&&!w.shiftKey&&!O&&(w.preventDefault(),G())},oe=w=>{z(w.target.value)},C=()=>{h&&h()};return d.jsxs("div",{className:q("bg-white",g),children:[s&&d.jsx("div",{className:"px-4 py-2 bg-blue-50 border-t border-blue-200",children:d.jsxs("div",{className:"flex items-start justify-between",children:[d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[d.jsx("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),d.jsx("span",{className:"text-sm font-medium text-blue-800",children:"已选择报告内容"})]}),d.jsx("div",{className:"text-sm text-blue-700 bg-white rounded px-2 py-1 max-h-20 overflow-y-auto",children:d.jsx("div",{className:"prose prose-sm max-w-none",dangerouslySetInnerHTML:{__html:s}})})]}),d.jsx("button",{onClick:C,className:"ml-2 p-1 rounded hover:bg-blue-100 text-blue-600",title:"清除选择",children:d.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}),d.jsxs("div",{className:"p-4",children:[d.jsxs("div",{className:"flex items-end space-x-3",children:[d.jsxs("div",{className:"flex-1 relative",children:[d.jsx("textarea",{ref:A,value:_,onChange:oe,onKeyDown:Q,onCompositionStart:()=>D(!0),onCompositionEnd:()=>D(!1),placeholder:r,disabled:c,rows:1,className:q("w-full px-4 py-3 pr-12 text-sm","border border-gray-300 rounded-2xl resize-none","bg-white placeholder-gray-500","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent","transition-colors duration-200","disabled:bg-gray-50 disabled:text-gray-400 disabled:cursor-not-allowed"),style:{minHeight:"48px",maxHeight:"120px"}}),_.length>0&&d.jsx("div",{className:"absolute bottom-1 right-12 text-xs text-gray-400",children:_.length})]}),d.jsx("button",{onClick:G,disabled:c||!_.trim(),className:q("flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center btn-hover",_.trim()&&!c?"bg-gradient-to-r from-primary-600 to-blue-600 text-white hover:from-primary-700 hover:to-blue-700 shadow-lg":"bg-gray-200 text-gray-400 cursor-not-allowed"),title:"发送消息 (Enter)",children:d.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})]}),d.jsx("div",{className:"mt-2 text-xs text-gray-500 text-center",children:"按 Enter 发送，Shift + Enter 换行"})]})]})},S1=({isConnected:i,connectionStatus:c,taskId:r,className:s=""})=>{if(i&&c==="connected")return null;const g=(()=>{switch(c){case"connecting":return{color:"bg-yellow-50 border-yellow-200 text-yellow-800",icon:d.jsx("svg",{className:"w-4 h-4 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),title:"正在连接...",message:"正在建立与服务器的连接，请稍候"};case"disconnected":return{color:"bg-gray-50 border-gray-200 text-gray-800",icon:d.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18.364 5.636l-12.728 12.728m0-12.728l12.728 12.728"})}),title:"连接已断开",message:r?"请选择一个任务以建立连接":"未连接到服务器"};case"error":return{color:"bg-red-50 border-red-200 text-red-800",icon:d.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),title:"连接错误",message:"无法连接到服务器，请检查网络连接"};default:return null}})();return g?d.jsx("div",{className:q("mx-4 mt-2",s),children:d.jsx("div",{className:q("rounded-lg border p-3",g.color),children:d.jsxs("div",{className:"flex items-center space-x-2",children:[g.icon,d.jsxs("div",{children:[d.jsx("div",{className:"font-medium text-sm",children:g.title}),d.jsx("div",{className:"text-xs opacity-75",children:g.message})]})]})})}):null},j1=({className:i=""})=>{const{currentTask:c}=ws(),{messages:r,isConnected:s,connectionStatus:h,selectedContext:g,connect:_,sendMessage:z,setSelectedContext:O,clearMessages:D}=u1();re.useEffect(()=>{c!=null&&c.id?_(c.id):D()},[c==null?void 0:c.id,_,D]);const A=(G,Q)=>{G.trim()&&z(G,Q)},K=()=>{O(null)};return d.jsxs("div",{className:q("flex flex-col h-full bg-white overflow-hidden",i),style:{width:"300px",maxWidth:"300px",minWidth:"300px"},children:[d.jsx(v1,{taskTitle:(c==null?void 0:c.title)||"未选择任务",isConnected:s,connectionStatus:h}),d.jsx(S1,{isConnected:s,connectionStatus:h,taskId:c==null?void 0:c.id}),d.jsx("div",{className:"flex-1 min-h-0 overflow-hidden",children:d.jsx(b1,{messages:r,loading:h==="connecting"})}),d.jsx("div",{className:"border-t border-gray-200",children:d.jsx(p1,{onSend:A,disabled:!s||!c,placeholder:c?s?"输入消息...":"连接中...":"请先选择一个任务...",selectedContext:g||void 0,onContextClear:K})})]})},T1=({width:i,className:c=""})=>d.jsx("div",{className:q("flex flex-col h-full",c),style:i?{width:`${i}px`}:void 0,children:d.jsx(j1,{})}),M1=({activePanel:i,onPanelChange:c,className:r=""})=>{const s=[{id:"sidebar",label:"任务",icon:h=>d.jsx("svg",{className:q("w-6 h-6",h?"text-primary-600":"text-gray-600"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})},{id:"report",label:"报告",icon:h=>d.jsx("svg",{className:q("w-6 h-6",h?"text-primary-600":"text-gray-600"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{id:"chat",label:"对话",icon:h=>d.jsx("svg",{className:q("w-6 h-6",h?"text-primary-600":"text-gray-600"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}];return d.jsx("nav",{className:q("fixed bottom-0 left-0 right-0 z-50","bg-white border-t border-gray-200","safe-area-pb",r),children:d.jsx("div",{className:"flex justify-around py-2",children:s.map(h=>{const g=i===h.id;return d.jsxs("button",{onClick:()=>c(h.id),className:q("flex flex-col items-center py-2 px-4 rounded-lg min-w-0","transition-all duration-200",g?"bg-primary-50":"hover:bg-gray-50"),children:[d.jsx("div",{className:"mb-1",children:h.icon(g)}),d.jsx("span",{className:q("text-xs font-medium",g?"text-primary-600":"text-gray-600"),children:h.label}),g&&d.jsx("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"})]},h.id)})})})},N1=({className:i=""})=>{const[c,r]=re.useState("chat"),[s,h]=re.useState(!1),[g,_]=re.useState(!1);re.useEffect(()=>{const D=()=>{h(window.innerWidth<1024)};return D(),window.addEventListener("resize",D),()=>window.removeEventListener("resize",D)},[]);const z=D=>{r(D)},O=()=>{_(!g)};return d.jsxs("div",{className:q("h-screen bg-gray-50 overflow-hidden",i),style:{display:"grid",gridTemplateColumns:s?"1fr":g?"64px 1fr 300px":"320px 1fr 300px",gridTemplateRows:"1fr",width:"100vw",maxWidth:"100vw"},children:[d.jsx("div",{className:q("bg-white border-r border-gray-200 overflow-hidden",s?c==="sidebar"?"block":"hidden":"block"),style:{gridColumn:"1",gridRow:"1"},children:d.jsx(h1,{collapsed:g,onToggle:O,isMobile:s})}),d.jsx("div",{className:q("overflow-hidden",s?c==="report"?"block":"hidden":"block"),style:{gridColumn:"2",gridRow:"1"},children:d.jsx(y1,{})}),d.jsx("div",{className:q("bg-white border-l border-gray-200 overflow-hidden",s?c==="chat"?"block":"hidden":"block"),style:{gridColumn:"3",gridRow:"1"},children:d.jsx(T1,{})}),s&&d.jsx(M1,{activePanel:c,onPanelChange:z})]})};function _1(){return d.jsx(N1,{})}Rm.createRoot(document.getElementById("root")).render(d.jsx(re.StrictMode,{children:d.jsx(_1,{})}));
//# sourceMappingURL=index-BtPRZXpy.js.map
