{"name": "frontend", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@adobe/css-tools": {"version": "4.4.3", "resolved": "http://registry.m.jd.com/@adobe/css-tools/download/@adobe/css-tools-4.4.3.tgz", "integrity": "sha1-vuu++wJk/esy0wUqyuDg2UMVqaI=", "dev": true, "license": "MIT"}, "node_modules/@alloc/quick-lru": {"version": "5.2.0", "resolved": "http://registry.m.jd.com/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz", "integrity": "sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz", "integrity": "sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@asamuzakjp/css-color": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/@asamuzakjp/css-color/download/@asamuzakjp/css-color-3.2.0.tgz", "integrity": "sha1-zEL1uFxZP3nx+k8l0rmzIeYdF5Q=", "dev": true, "license": "MIT", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}}, "node_modules/@asamuzakjp/css-color/node_modules/lru-cache": {"version": "10.4.3", "resolved": "http://registry.m.jd.com/lru-cache/download/lru-cache-10.4.3.tgz", "integrity": "sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=", "dev": true, "license": "ISC"}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz", "integrity": "sha1-IA9xXmbVKiOyIalDVTSpHME61b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/compat-data/download/@babel/compat-data-7.28.0.tgz", "integrity": "sha1-n8b9WMKmoVJDzROYMiSWg5IHB5A=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/core/download/@babel/core-7.28.0.tgz", "integrity": "sha1-VdrYCNW/NEWhCO78iOo/3wNHSaQ=", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/generator/download/@babel/generator-7.28.0.tgz", "integrity": "sha1-nML3vW6wVNd9xmwmZBSKDFEYrNI=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "http://registry.m.jd.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.27.2.tgz", "integrity": "sha1-RqD276uAjVHSnOloWN0Qzocycz0=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-globals": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/helper-globals/download/@babel/helper-globals-7.28.0.tgz", "integrity": "sha1-uUMN8qpOF7woZl6t6uiqHZheZnQ=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz", "integrity": "sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "http://registry.m.jd.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.27.3.tgz", "integrity": "sha1-2wu8+6WAL573hwcFp++HiFCO3gI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz", "integrity": "sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz", "integrity": "sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz", "integrity": "sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.27.1.tgz", "integrity": "sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.28.2", "resolved": "http://registry.m.jd.com/@babel/helpers/download/@babel/helpers-7.28.2.tgz", "integrity": "sha1-gPCRj+y/6+qa+FbEGXYyMAQO6FA=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.28.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/parser/download/@babel/parser-7.28.0.tgz", "integrity": "sha1-l5gp+6tRop4TkB5agHE9vLhAgl4=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-transform-react-jsx-self/download/@babel/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "http://registry.m.jd.com/@babel/plugin-transform-react-jsx-source/download/@babel/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.28.2", "resolved": "http://registry.m.jd.com/@babel/runtime/download/@babel/runtime-7.28.2.tgz", "integrity": "sha1-KuWp1RzFg70fVnOzu3DW2BloJHM=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "http://registry.m.jd.com/@babel/template/download/@babel/template-7.27.2.tgz", "integrity": "sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@babel/traverse/download/@babel/traverse-7.28.0.tgz", "integrity": "sha1-UYqhEzWbBiBCN54zPbGDgLU340s=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.28.2", "resolved": "http://registry.m.jd.com/@babel/types/download/@babel/types-7.28.2.tgz", "integrity": "sha1-2p2whWqaiOChOwGYgddRNYjPcSs=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@csstools/color-helpers": {"version": "5.0.2", "resolved": "http://registry.m.jd.com/@csstools/color-helpers/download/@csstools/color-helpers-5.0.2.tgz", "integrity": "sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT-0", "engines": {"node": ">=18"}}, "node_modules/@csstools/css-calc": {"version": "2.1.4", "resolved": "http://registry.m.jd.com/@csstools/css-calc/download/@csstools/css-calc-2.1.4.tgz", "integrity": "sha1-hHP2Pi/NbkWYON1BJAHVlI8iTGU=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-color-parser": {"version": "3.0.10", "resolved": "http://registry.m.jd.com/@csstools/css-color-parser/download/@csstools/css-color-parser-3.0.10.tgz", "integrity": "sha1-efxohk3UPDtngtKzgovA+p0IXBA=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "dependencies": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}, "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-parser-algorithms": {"version": "3.0.5", "resolved": "http://registry.m.jd.com/@csstools/css-parser-algorithms/download/@csstools/css-parser-algorithms-3.0.5.tgz", "integrity": "sha1-V1U3Cpopq67FUVtDyLPyz5wuMHY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.4"}}, "node_modules/@csstools/css-tokenizer": {"version": "3.0.4", "resolved": "http://registry.m.jd.com/@csstools/css-tokenizer/download/@csstools/css-tokenizer-3.0.4.tgz", "integrity": "sha1-Mz/tq8P9Go5dAQABNzHPGeaoxdM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.8", "resolved": "http://registry.m.jd.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.8.tgz", "integrity": "sha1-peElLKKYPVZq8cDqOa3tZXNvxm0=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "http://registry.m.jd.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "http://registry.m.jd.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.21.0", "resolved": "http://registry.m.jd.com/@eslint/config-array/download/@eslint/config-array-0.21.0.tgz", "integrity": "sha1-q9vL0WsSTGOAgXZjkqTWtQn3JjY=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-helpers": {"version": "0.3.0", "resolved": "http://registry.m.jd.com/@eslint/config-helpers/download/@eslint/config-helpers-0.3.0.tgz", "integrity": "sha1-PgmpDfuH4ABcdpR5HljpcHcnEoY=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.15.1", "resolved": "http://registry.m.jd.com/@eslint/core/download/@eslint/core-0.15.1.tgz", "integrity": "sha1-1TDUQgnL/i+C74bWugh2AZbdO2A=", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/@eslint/eslintrc/download/@eslint/eslintrc-3.3.1.tgz", "integrity": "sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "http://registry.m.jd.com/globals/download/globals-14.0.0.tgz", "integrity": "sha1-iY10E8Kbq89rr+Vvyt3thYrack4=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "9.32.0", "resolved": "http://registry.m.jd.com/@eslint/js/download/@eslint/js-9.32.0.tgz", "integrity": "sha1-oCkW9YvVh+onaHbLBRtXmj110JE=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "http://registry.m.jd.com/@eslint/object-schema/download/@eslint/object-schema-2.1.6.tgz", "integrity": "sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.4", "resolved": "http://registry.m.jd.com/@eslint/plugin-kit/download/@eslint/plugin-kit-0.3.4.tgz", "integrity": "sha1-xrnxZelL9Nn91JPxwCipSq9fwcw=", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.15.1", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@floating-ui/core": {"version": "1.7.3", "resolved": "http://registry.m.jd.com/@floating-ui/core/download/@floating-ui/core-1.7.3.tgz", "integrity": "sha1-Ri1yLwAeI+Rthv0r0NIbdpPMuLc=", "license": "MIT", "dependencies": {"@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/dom": {"version": "1.7.3", "resolved": "http://registry.m.jd.com/@floating-ui/dom/download/@floating-ui/dom-1.7.3.tgz", "integrity": "sha1-YXSsNAnmoGS73x9LsHGI7pRh+M8=", "license": "MIT", "dependencies": {"@floating-ui/core": "^1.7.3", "@floating-ui/utils": "^0.2.10"}}, "node_modules/@floating-ui/react": {"version": "0.26.28", "resolved": "http://registry.m.jd.com/@floating-ui/react/download/@floating-ui/react-0.26.28.tgz", "integrity": "sha1-k/ROuusCQJMS6d+VB+g6q0qMDcc=", "license": "MIT", "dependencies": {"@floating-ui/react-dom": "^2.1.2", "@floating-ui/utils": "^0.2.8", "tabbable": "^6.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/react-dom": {"version": "2.1.5", "resolved": "http://registry.m.jd.com/@floating-ui/react-dom/download/@floating-ui/react-dom-2.1.5.tgz", "integrity": "sha1-0R43JtLrOF2M8yFjSHQpB8HUn88=", "license": "MIT", "dependencies": {"@floating-ui/dom": "^1.7.3"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}}, "node_modules/@floating-ui/utils": {"version": "0.2.10", "resolved": "http://registry.m.jd.com/@floating-ui/utils/download/@floating-ui/utils-0.2.10.tgz", "integrity": "sha1-oqHjgS0UUl9yXQEac+zrQf71vBw=", "license": "MIT"}, "node_modules/@headlessui/react": {"version": "2.2.4", "resolved": "http://registry.m.jd.com/@headlessui/react/download/@headlessui/react-2.2.4.tgz", "integrity": "sha1-mwxIpb4f5Fd/A/MhYpC7cdmINSA=", "license": "MIT", "dependencies": {"@floating-ui/react": "^0.26.16", "@react-aria/focus": "^3.20.2", "@react-aria/interactions": "^3.25.0", "@tanstack/react-virtual": "^3.13.9", "use-sync-external-store": "^1.5.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"react": "^18 || ^19 || ^19.0.0-rc", "react-dom": "^18 || ^19 || ^19.0.0-rc"}}, "node_modules/@heroicons/react": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/@heroicons/react/download/@heroicons/react-2.2.0.tgz", "integrity": "sha1-DAUSSvUENKgAdzq+yNOvail9kEs=", "license": "MIT", "peerDependencies": {"react": ">= 16 || ^19.0.0-rc"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "http://registry.m.jd.com/@humanfs/core/download/@humanfs/core-0.19.1.tgz", "integrity": "sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "http://registry.m.jd.com/@humanfs/node/download/@humanfs/node-0.16.6.tgz", "integrity": "sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.3.1.tgz", "integrity": "sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.3", "resolved": "http://registry.m.jd.com/@humanwhocodes/retry/download/@humanwhocodes/retry-0.4.3.tgz", "integrity": "sha1-wrnS43TuYsWG062+qHGZsdenpro=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "resolved": "http://registry.m.jd.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz", "integrity": "sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "resolved": "http://registry.m.jd.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.12.tgz", "integrity": "sha1-IjTOJsYoifA9s9f+pDwZMqs+kns=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz", "integrity": "sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "http://registry.m.jd.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.4.tgz", "integrity": "sha1-c1gENDOy5dpWmqAsvEwSHaOvJ9c=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "resolved": "http://registry.m.jd.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.29.tgz", "integrity": "sha1-pY0x6q2vksZpVoCy4dRkqbj79/w=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://registry.m.jd.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "resolved": "http://registry.m.jd.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz", "integrity": "sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@polka/url": {"version": "1.0.0-next.29", "resolved": "http://registry.m.jd.com/@polka/url/download/@polka/url-1.0.0-next.29.tgz", "integrity": "sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=", "dev": true, "license": "MIT"}, "node_modules/@react-aria/focus": {"version": "3.21.0", "resolved": "http://registry.m.jd.com/@react-aria/focus/download/@react-aria/focus-3.21.0.tgz", "integrity": "sha1-1bwye+4l6YGTTqDdsd774CCoT2o=", "license": "Apache-2.0", "dependencies": {"@react-aria/interactions": "^3.25.4", "@react-aria/utils": "^3.30.0", "@react-types/shared": "^3.31.0", "@swc/helpers": "^0.5.0", "clsx": "^2.0.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@react-aria/interactions": {"version": "3.25.4", "resolved": "http://registry.m.jd.com/@react-aria/interactions/download/@react-aria/interactions-3.25.4.tgz", "integrity": "sha1-Lw4h6Bh7fwlEsyP1VpbK6azLOeA=", "license": "Apache-2.0", "dependencies": {"@react-aria/ssr": "^3.9.10", "@react-aria/utils": "^3.30.0", "@react-stately/flags": "^3.1.2", "@react-types/shared": "^3.31.0", "@swc/helpers": "^0.5.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@react-aria/ssr": {"version": "3.9.10", "resolved": "http://registry.m.jd.com/@react-aria/ssr/download/@react-aria/ssr-3.9.10.tgz", "integrity": "sha1-f9wJ6BGUTODfHX5xPeFEmr10NeY=", "license": "Apache-2.0", "dependencies": {"@swc/helpers": "^0.5.0"}, "engines": {"node": ">= 12"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@react-aria/utils": {"version": "3.30.0", "resolved": "http://registry.m.jd.com/@react-aria/utils/download/@react-aria/utils-3.30.0.tgz", "integrity": "sha1-aKodcDyeBGg1C9HjtYPZnp5peVo=", "license": "Apache-2.0", "dependencies": {"@react-aria/ssr": "^3.9.10", "@react-stately/flags": "^3.1.2", "@react-stately/utils": "^3.10.8", "@react-types/shared": "^3.31.0", "@swc/helpers": "^0.5.0", "clsx": "^2.0.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@react-stately/flags": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/@react-stately/flags/download/@react-stately/flags-3.1.2.tgz", "integrity": "sha1-XI5a5BbTfTfi5YPS/LOgRik1BPI=", "license": "Apache-2.0", "dependencies": {"@swc/helpers": "^0.5.0"}}, "node_modules/@react-stately/utils": {"version": "3.10.8", "resolved": "http://registry.m.jd.com/@react-stately/utils/download/@react-stately/utils-3.10.8.tgz", "integrity": "sha1-/bnRcve7wtCD5pGQ9e8O36S0OS8=", "license": "Apache-2.0", "dependencies": {"@swc/helpers": "^0.5.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@react-types/shared": {"version": "3.31.0", "resolved": "http://registry.m.jd.com/@react-types/shared/download/@react-types/shared-3.31.0.tgz", "integrity": "sha1-AUvlMJbDco8GhFUEMIB+mWI2XBU=", "license": "Apache-2.0", "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.27", "resolved": "http://registry.m.jd.com/@rolldown/pluginutils/download/@rolldown/pluginutils-1.0.0-beta.27.tgz", "integrity": "sha1-R9K/TO9tRwsi9YMbQg+JZOC/dV8=", "dev": true, "license": "MIT"}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.46.2", "resolved": "http://registry.m.jd.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.46.2.tgz", "integrity": "sha1-mNkERSgt7FT9BUQDBaXo33mpHs4=", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@swc/helpers": {"version": "0.5.17", "resolved": "http://registry.m.jd.com/@swc/helpers/download/@swc/helpers-0.5.17.tgz", "integrity": "sha1-WnvpWsDwvxhufm6JDnpvbNps6XE=", "license": "Apache-2.0", "dependencies": {"tslib": "^2.8.0"}}, "node_modules/@tanstack/react-virtual": {"version": "3.13.12", "resolved": "http://registry.m.jd.com/@tanstack/react-virtual/download/@tanstack/react-virtual-3.13.12.tgz", "integrity": "sha1-03LcJ4NznMBOwacoyoIDk3aHqBk=", "license": "MIT", "dependencies": {"@tanstack/virtual-core": "3.13.12"}, "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/@tanstack/virtual-core": {"version": "3.13.12", "resolved": "http://registry.m.jd.com/@tanstack/virtual-core/download/@tanstack/virtual-core-3.13.12.tgz", "integrity": "sha1-Hf8XbfnMj5PHjF5GvOoRB5s5dXg=", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}}, "node_modules/@testing-library/dom": {"version": "10.4.1", "resolved": "http://registry.m.jd.com/@testing-library/dom/download/@testing-library/dom-10.4.1.tgz", "integrity": "sha1-1ET4qInppG6aO087iOD8s++2z5U=", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "picocolors": "1.1.1", "pretty-format": "^27.0.2"}, "engines": {"node": ">=18"}}, "node_modules/@testing-library/jest-dom": {"version": "6.6.4", "resolved": "http://registry.m.jd.com/@testing-library/jest-dom/download/@testing-library/jest-dom-6.6.4.tgz", "integrity": "sha1-V3oXYXaL2lRYxCJBrdOxVww005w=", "dev": true, "license": "MIT", "dependencies": {"@adobe/css-tools": "^4.4.0", "aria-query": "^5.0.0", "css.escape": "^1.5.1", "dom-accessibility-api": "^0.6.3", "lodash": "^4.17.21", "picocolors": "^1.1.1", "redent": "^3.0.0"}, "engines": {"node": ">=14", "npm": ">=6", "yarn": ">=1"}}, "node_modules/@testing-library/jest-dom/node_modules/dom-accessibility-api": {"version": "0.6.3", "resolved": "http://registry.m.jd.com/dom-accessibility-api/download/dom-accessibility-api-0.6.3.tgz", "integrity": "sha1-mT6SXMHXPyxmLn113VpURSWaj9g=", "dev": true, "license": "MIT"}, "node_modules/@testing-library/react": {"version": "16.3.0", "resolved": "http://registry.m.jd.com/@testing-library/react/download/@testing-library/react-16.3.0.tgz", "integrity": "sha1-OoW7m96/GAzXbboWRU4kJWTVmKY=", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "engines": {"node": ">=18"}, "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@testing-library/user-event": {"version": "14.6.1", "resolved": "http://registry.m.jd.com/@testing-library/user-event/download/@testing-library/user-event-14.6.1.tgz", "integrity": "sha1-E+CaMteotwYP44MEeI6/QZfNIUk=", "dev": true, "license": "MIT", "engines": {"node": ">=12", "npm": ">=6"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}}, "node_modules/@types/aria-query": {"version": "5.0.4", "resolved": "http://registry.m.jd.com/@types/aria-query/download/@types/aria-query-5.0.4.tgz", "integrity": "sha1-GjHD03iFDSd42rtjdNA23LpLpwg=", "dev": true, "license": "MIT", "peer": true}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "http://registry.m.jd.com/@types/babel__core/download/@types/babel__core-7.20.5.tgz", "integrity": "sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "http://registry.m.jd.com/@types/babel__generator/download/@types/babel__generator-7.27.0.tgz", "integrity": "sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "http://registry.m.jd.com/@types/babel__template/download/@types/babel__template-7.4.4.tgz", "integrity": "sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.28.0", "resolved": "http://registry.m.jd.com/@types/babel__traverse/download/@types/babel__traverse-7.28.0.tgz", "integrity": "sha1-B9cT1szg0mXJhJ2wy+YtP2Hzb3Q=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.28.2"}}, "node_modules/@types/chai": {"version": "5.2.2", "resolved": "http://registry.m.jd.com/@types/chai/download/@types/chai-5.2.2.tgz", "integrity": "sha1-bxTOoYGA/8RBa8D9Er4F/dc73Ws=", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/@types/deep-eql/download/@types/deep-eql-4.0.2.tgz", "integrity": "sha1-M0MRlx06BxIefrkbaEpgXn7qnL0=", "dev": true, "license": "MIT"}, "node_modules/@types/dompurify": {"version": "3.0.5", "resolved": "http://registry.m.jd.com/@types/dompurify/download/@types/dompurify-3.0.5.tgz", "integrity": "sha1-AgaaL8uJoWO6zxp4j3PLQV3XXLc=", "dev": true, "license": "MIT", "dependencies": {"@types/trusted-types": "*"}}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "http://registry.m.jd.com/@types/estree/download/@types/estree-1.0.8.tgz", "integrity": "sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "http://registry.m.jd.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/react": {"version": "19.1.9", "resolved": "http://registry.m.jd.com/@types/react/download/@types/react-19.1.9.tgz", "integrity": "sha1-9Csk81R0Vmo5tcOpjk0MQlt5qEk=", "devOptional": true, "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.7", "resolved": "http://registry.m.jd.com/@types/react-dom/download/@types/react-dom-19.1.7.tgz", "integrity": "sha1-KGPyqongI1krmBIE75LFIhsoZBA=", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "http://registry.m.jd.com/@types/trusted-types/download/@types/trusted-types-2.0.7.tgz", "integrity": "sha1-usywepcLkXB986PoumiWxX6tLRE=", "devOptional": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.39.0.tgz", "integrity": "sha1-ya/sGGbuGm6j12i1+OkiAe+7ugY=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.39.0", "@typescript-eslint/type-utils": "8.39.0", "@typescript-eslint/utils": "8.39.0", "@typescript-eslint/visitor-keys": "8.39.0", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.39.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.5", "resolved": "http://registry.m.jd.com/ignore/download/ignore-7.0.5.tgz", "integrity": "sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.39.0.tgz", "integrity": "sha1-xLiV16R/TNXubud+ow5h1YuAIAg=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.39.0", "@typescript-eslint/types": "8.39.0", "@typescript-eslint/typescript-estree": "8.39.0", "@typescript-eslint/visitor-keys": "8.39.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/project-service/download/@typescript-eslint/project-service-8.39.0.tgz", "integrity": "sha1-ccspw/gTn5mpBbhwUSe//CroR1k=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.39.0", "@typescript-eslint/types": "^8.39.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.39.0.tgz", "integrity": "sha1-ukv22CV7vBcsKY/r8WvCLfSFZXA=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.39.0", "@typescript-eslint/visitor-keys": "8.39.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/tsconfig-utils/download/@typescript-eslint/tsconfig-utils-8.39.0.tgz", "integrity": "sha1-suh/70GjBnxXBTO3IvavR74hPxM=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.39.0.tgz", "integrity": "sha1-MQ7Hga5ee7D1lAv9ZSVzWH8ieGs=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.39.0", "@typescript-eslint/typescript-estree": "8.39.0", "@typescript-eslint/utils": "8.39.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/types/download/@typescript-eslint/types-8.39.0.tgz", "integrity": "sha1-gPAQtxadQ0qRzQUp1wpSjbycmcY=", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.39.0.tgz", "integrity": "sha1-uUd6XEeg/s7/6RrfVTrZo81Ms9Y=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.39.0", "@typescript-eslint/tsconfig-utils": "8.39.0", "@typescript-eslint/types": "8.39.0", "@typescript-eslint/visitor-keys": "8.39.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {"version": "9.0.5", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.7.2", "resolved": "http://registry.m.jd.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.39.0.tgz", "integrity": "sha1-3+pC88fshfnz6ZT/C7qPOy8J4iA=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.39.0", "@typescript-eslint/types": "8.39.0", "@typescript-eslint/typescript-estree": "8.39.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.39.0.tgz", "integrity": "sha1-XWGaboEM3T/RkTYycZy8yrCL+HU=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.39.0", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@vitejs/plugin-react": {"version": "4.7.0", "resolved": "http://registry.m.jd.com/@vitejs/plugin-react/download/@vitejs/plugin-react-4.7.0.tgz", "integrity": "sha1-ZHr057t1rTrdV452KtmEuQ9KJLk=", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.28.0", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.27", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0"}}, "node_modules/@vitest/expect": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/expect/download/@vitest/expect-3.2.4.tgz", "integrity": "sha1-g2ISTNgRpe4RxXaCB7nfU9NPJDM=", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/mocker": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/mocker/download/@vitest/mocker-3.2.4.tgz", "integrity": "sha1-RHHE771i2w1PogPmXMawWKhcq9M=", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "3.2.4", "estree-walker": "^3.0.3", "magic-string": "^0.30.17"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"msw": "^2.4.9", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "peerDependenciesMeta": {"msw": {"optional": true}, "vite": {"optional": true}}}, "node_modules/@vitest/pretty-format": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/pretty-format/download/@vitest/pretty-format-3.2.4.tgz", "integrity": "sha1-PBAveegrIEomx6WSG/R9U0kZ07Q=", "dev": true, "license": "MIT", "dependencies": {"tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/runner/download/@vitest/runner-3.2.4.tgz", "integrity": "sha1-XOAnTySpcfZQD2/BZtU9g4JDB2Y=", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "3.2.4", "pathe": "^2.0.3", "strip-literal": "^3.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/snapshot": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/snapshot/download/@vitest/snapshot-3.2.4.tgz", "integrity": "sha1-QKi8A0asCu6SPA7vwtwAXZC8mHw=", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "magic-string": "^0.30.17", "pathe": "^2.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/spy/download/@vitest/spy-3.2.4.tgz", "integrity": "sha1-zBjyb0Dz8CjaZiAEaIH05FGMJZk=", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^4.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/ui": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/ui/download/@vitest/ui-3.2.4.tgz", "integrity": "sha1-34CAU3wdz+rjU7LTyzMB2ayv4Eo=", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "3.2.4", "fflate": "^0.8.2", "flatted": "^3.3.3", "pathe": "^2.0.3", "sirv": "^3.0.1", "tinyglobby": "^0.2.14", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"vitest": "3.2.4"}}, "node_modules/@vitest/utils": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/@vitest/utils/download/@vitest/utils-3.2.4.tgz", "integrity": "sha1-wIE7xC2ZUn+4xbE4x6iFFrykb+o=", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "loupe": "^3.1.4", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "http://registry.m.jd.com/acorn/download/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/agent-base": {"version": "7.1.4", "resolved": "http://registry.m.jd.com/agent-base/download/agent-base-7.1.4.tgz", "integrity": "sha1-48121MVI7oldPD/Y3B9sW5Ay56g=", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8=", "dev": true, "license": "MIT"}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/anymatch/download/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "5.0.2", "resolved": "http://registry.m.jd.com/arg/download/arg-5.0.2.tgz", "integrity": "sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/aria-query": {"version": "5.3.0", "resolved": "http://registry.m.jd.com/aria-query/download/aria-query-5.3.0.tgz", "integrity": "sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=", "dev": true, "license": "Apache-2.0", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/assertion-error": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/assertion-error/download/assertion-error-2.0.1.tgz", "integrity": "sha1-9kGhlrM1aQsQcL8AtudZP+wZC/c=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/autoprefixer": {"version": "10.4.21", "resolved": "http://registry.m.jd.com/autoprefixer/download/autoprefixer-10.4.21.tgz", "integrity": "sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/binary-extensions/download/binary-extensions-2.3.0.tgz", "integrity": "sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.12.tgz", "integrity": "sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "http://registry.m.jd.com/browserslist/download/browserslist-4.25.1.tgz", "integrity": "sha1-up6ObymKHYb4Kcm5deB5SJZ7sRE=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/cac": {"version": "6.7.14", "resolved": "http://registry.m.jd.com/cac/download/cac-6.7.14.tgz", "integrity": "sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase-css": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/camelcase-css/download/camelcase-css-2.0.1.tgz", "integrity": "sha1-7pePaUeRTMMMa0R0G27R338EP9U=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001733", "resolved": "http://registry.m.jd.com/caniuse-lite/download/caniuse-lite-1.0.30001733.tgz", "integrity": "sha1-kYQF7WZHpihA+zKIMs9aA/mGl0s=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chai": {"version": "5.2.1", "resolved": "http://registry.m.jd.com/chai/download/chai-5.2.1.tgz", "integrity": "sha1-qVAkYr3HnPkLSglTU3qZCKpji0c=", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/check-error": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/check-error/download/check-error-2.1.1.tgz", "integrity": "sha1-h+uHauce44j6BHH+Qj9JS+HZbMw=", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "http://registry.m.jd.com/chokidar/download/chokidar-3.6.0.tgz", "integrity": "sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/clsx": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/clsx/download/clsx-2.1.1.tgz", "integrity": "sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "4.1.1", "resolved": "http://registry.m.jd.com/commander/download/commander-4.1.1.tgz", "integrity": "sha1-n9YCvZNilOnp70aj9NaWQESxgGg=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/convert-source-map/download/convert-source-map-2.0.0.tgz", "integrity": "sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/cookie/download/cookie-1.0.2.tgz", "integrity": "sha1-JzYHAVMhFr0/H5QWkp0Xav4eRhA=", "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "http://registry.m.jd.com/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css.escape": {"version": "1.5.1", "resolved": "http://registry.m.jd.com/css.escape/download/css.escape-1.5.1.tgz", "integrity": "sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=", "dev": true, "license": "MIT"}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/cssesc/download/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssstyle": {"version": "4.6.0", "resolved": "http://registry.m.jd.com/cssstyle/download/cssstyle-4.6.0.tgz", "integrity": "sha1-6hgAcCTjFn9PEFMV8+wtmCv0jtk=", "dev": true, "license": "MIT", "dependencies": {"@asamuzakjp/css-color": "^3.2.0", "rrweb-cssom": "^0.8.0"}, "engines": {"node": ">=18"}}, "node_modules/csstype": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/csstype/download/csstype-3.1.3.tgz", "integrity": "sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=", "devOptional": true, "license": "MIT"}, "node_modules/data-urls": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/data-urls/download/data-urls-5.0.0.tgz", "integrity": "sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=", "dev": true, "license": "MIT", "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}, "engines": {"node": ">=18"}}, "node_modules/date-fns": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/date-fns/download/date-fns-4.1.0.tgz", "integrity": "sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=", "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/kossnocorp"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.6.0", "resolved": "http://registry.m.jd.com/decimal.js/download/decimal.js-10.6.0.tgz", "integrity": "sha1-5kmkPjq5U6chkv9Zg4ZeUJ837Zo=", "dev": true, "license": "MIT"}, "node_modules/deep-eql": {"version": "5.0.2", "resolved": "http://registry.m.jd.com/deep-eql/download/deep-eql-5.0.2.tgz", "integrity": "sha1-S3VtjXcKklcwCCXVKiws/5nDo0E=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/dequal": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/dequal/download/dequal-2.0.3.tgz", "integrity": "sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/detect-libc": {"version": "2.0.4", "resolved": "http://registry.m.jd.com/detect-libc/download/detect-libc-2.0.4.tgz", "integrity": "sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=", "dev": true, "license": "Apache-2.0", "optional": true, "peer": true, "engines": {"node": ">=8"}}, "node_modules/didyoumean": {"version": "1.2.2", "resolved": "http://registry.m.jd.com/didyoumean/download/didyoumean-1.2.2.tgz", "integrity": "sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=", "dev": true, "license": "Apache-2.0"}, "node_modules/dlv": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/dlv/download/dlv-1.1.3.tgz", "integrity": "sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=", "dev": true, "license": "MIT"}, "node_modules/dom-accessibility-api": {"version": "0.5.16", "resolved": "http://registry.m.jd.com/dom-accessibility-api/download/dom-accessibility-api-0.5.16.tgz", "integrity": "sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=", "dev": true, "license": "MIT", "peer": true}, "node_modules/dompurify": {"version": "3.2.6", "resolved": "http://registry.m.jd.com/dompurify/download/dompurify-3.2.6.tgz", "integrity": "sha1-ygQKatK4jiqS3EXzjHn4SnFKHK0=", "license": "(MPL-2.0 OR Apache-2.0)", "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "resolved": "http://registry.m.jd.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz", "integrity": "sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=", "dev": true, "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.199", "resolved": "http://registry.m.jd.com/electron-to-chromium/download/electron-to-chromium-1.5.199.tgz", "integrity": "sha1-TYvpx4NiwF8JXrc5LppU8fsU/To=", "dev": true, "license": "ISC"}, "node_modules/emoji-regex": {"version": "9.2.2", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-9.2.2.tgz", "integrity": "sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=", "dev": true, "license": "MIT"}, "node_modules/entities": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/entities/download/entities-6.0.1.tgz", "integrity": "sha1-wow0pDN5yn9h0HQTCy9fcCCjBpQ=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "http://registry.m.jd.com/es-module-lexer/download/es-module-lexer-1.7.0.tgz", "integrity": "sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=", "dev": true, "license": "MIT"}, "node_modules/esbuild": {"version": "0.25.8", "resolved": "http://registry.m.jd.com/esbuild/download/esbuild-0.25.8.tgz", "integrity": "sha1-SC1CGYtCfJwvOoG2PXZjrssd2gc=", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.8", "@esbuild/android-arm": "0.25.8", "@esbuild/android-arm64": "0.25.8", "@esbuild/android-x64": "0.25.8", "@esbuild/darwin-arm64": "0.25.8", "@esbuild/darwin-x64": "0.25.8", "@esbuild/freebsd-arm64": "0.25.8", "@esbuild/freebsd-x64": "0.25.8", "@esbuild/linux-arm": "0.25.8", "@esbuild/linux-arm64": "0.25.8", "@esbuild/linux-ia32": "0.25.8", "@esbuild/linux-loong64": "0.25.8", "@esbuild/linux-mips64el": "0.25.8", "@esbuild/linux-ppc64": "0.25.8", "@esbuild/linux-riscv64": "0.25.8", "@esbuild/linux-s390x": "0.25.8", "@esbuild/linux-x64": "0.25.8", "@esbuild/netbsd-arm64": "0.25.8", "@esbuild/netbsd-x64": "0.25.8", "@esbuild/openbsd-arm64": "0.25.8", "@esbuild/openbsd-x64": "0.25.8", "@esbuild/openharmony-arm64": "0.25.8", "@esbuild/sunos-x64": "0.25.8", "@esbuild/win32-arm64": "0.25.8", "@esbuild/win32-ia32": "0.25.8", "@esbuild/win32-x64": "0.25.8"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/escalade/download/escalade-3.2.0.tgz", "integrity": "sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.32.0", "resolved": "http://registry.m.jd.com/eslint/download/eslint-9.32.0.tgz", "integrity": "sha1-TqKN9KjbxFThJR4POu1Lz0zlCkc=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.21.0", "@eslint/config-helpers": "^0.3.0", "@eslint/core": "^0.15.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.32.0", "@eslint/plugin-kit": "^0.3.4", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-plugin-react-hooks": {"version": "5.2.0", "resolved": "http://registry.m.jd.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-5.2.0.tgz", "integrity": "sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0"}}, "node_modules/eslint-plugin-react-refresh": {"version": "0.4.20", "resolved": "http://registry.m.jd.com/eslint-plugin-react-refresh/download/eslint-plugin-react-refresh-0.4.20.tgz", "integrity": "sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=", "dev": true, "license": "MIT", "peerDependencies": {"eslint": ">=8.40"}}, "node_modules/eslint-scope": {"version": "8.4.0", "resolved": "http://registry.m.jd.com/eslint-scope/download/eslint-scope-8.4.0.tgz", "integrity": "sha1-iOZGogf61hQ2/6OetQUUcgBlXII=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "10.4.0", "resolved": "http://registry.m.jd.com/espree/download/espree-10.4.0.tgz", "integrity": "sha1-1U9JSdRikAWh+haNk3w/8ffiqDc=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "http://registry.m.jd.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "http://registry.m.jd.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/estree-walker/download/estree-walker-3.0.3.tgz", "integrity": "sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/expect-type": {"version": "1.2.2", "resolved": "http://registry.m.jd.com/expect-type/download/expect-type-1.2.2.tgz", "integrity": "sha1-wDCjKfthGEEmyER1hbx1p+xvv/M=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/fast-glob/download/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "http://registry.m.jd.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "http://registry.m.jd.com/fastq/download/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fflate": {"version": "0.8.2", "resolved": "http://registry.m.jd.com/fflate/download/fflate-0.8.2.tgz", "integrity": "sha1-/IYx9TR4Eq1gKLvkojCLJ5KqHeo=", "dev": true, "license": "MIT"}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-8.0.0.tgz", "integrity": "sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/find-up/download/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/flat-cache/download/flat-cache-4.0.1.tgz", "integrity": "sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/foreground-child": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/foreground-child/download/foreground-child-3.3.1.tgz", "integrity": "sha1-Mujp7Rtoo0l777msK2rfkqY4V28=", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/fraction.js": {"version": "4.3.7", "resolved": "http://registry.m.jd.com/fraction.js/download/fraction.js-4.3.7.tgz", "integrity": "sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "http://registry.m.jd.com/fsevents/download/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "http://registry.m.jd.com/function-bind/download/function-bind-1.1.2.tgz", "integrity": "sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "http://registry.m.jd.com/gensync/download/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/glob": {"version": "10.4.5", "resolved": "http://registry.m.jd.com/glob/download/glob-10.4.5.tgz", "integrity": "sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.2.tgz", "integrity": "sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/glob/node_modules/minimatch": {"version": "9.0.5", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-9.0.5.tgz", "integrity": "sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "15.15.0", "resolved": "http://registry.m.jd.com/globals/download/globals-15.15.0.tgz", "integrity": "sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/graphemer/download/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/hasown/download/hasown-2.0.2.tgz", "integrity": "sha1-AD6vkb563DcuhOxZ3DclLO24AAM=", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/html-encoding-sniffer": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/html-encoding-sniffer/download/html-encoding-sniffer-4.0.0.tgz", "integrity": "sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^3.1.1"}, "engines": {"node": ">=18"}}, "node_modules/http-proxy-agent": {"version": "7.0.2", "resolved": "http://registry.m.jd.com/http-proxy-agent/download/http-proxy-agent-7.0.2.tgz", "integrity": "sha1-mosfJGhmwChQlIZYX2K48sGMJw4=", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "node_modules/https-proxy-agent": {"version": "7.0.6", "resolved": "http://registry.m.jd.com/https-proxy-agent/download/https-proxy-agent-7.0.6.tgz", "integrity": "sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "http://registry.m.jd.com/iconv-lite/download/iconv-lite-0.6.3.tgz", "integrity": "sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immer": {"version": "10.1.1", "resolved": "http://registry.m.jd.com/immer/download/immer-10.1.1.tgz", "integrity": "sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/indent-string/download/indent-string-4.0.0.tgz", "integrity": "sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "http://registry.m.jd.com/is-core-module/download/is-core-module-2.16.1.tgz", "integrity": "sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz", "integrity": "sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/jackspeak": {"version": "3.4.3", "resolved": "http://registry.m.jd.com/jackspeak/download/jackspeak-3.4.3.tgz", "integrity": "sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jiti": {"version": "2.5.1", "resolved": "http://registry.m.jd.com/jiti/download/jiti-2.5.1.tgz", "integrity": "sha1-vQmcHCvhxZu+pOWtzRJzY0RnWdA=", "dev": true, "license": "MIT", "optional": true, "peer": true, "bin": {"jiti": "lib/jiti-cli.mjs"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsdom": {"version": "26.1.0", "resolved": "http://registry.m.jd.com/jsdom/download/jsdom-26.1.0.tgz", "integrity": "sha1-q18cHK/AS9h4clSQl06l6L8McrM=", "dev": true, "license": "MIT", "dependencies": {"cssstyle": "^4.2.1", "data-urls": "^5.0.0", "decimal.js": "^10.5.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.16", "parse5": "^7.2.1", "rrweb-cssom": "^0.8.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^5.1.1", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.1.1", "ws": "^8.18.0", "xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"canvas": "^3.0.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/jsesc/download/jsesc-3.1.0.tgz", "integrity": "sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/json-buffer/download/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "http://registry.m.jd.com/json5/download/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "http://registry.m.jd.com/keyv/download/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/levn/download/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lightningcss": {"version": "1.30.1", "resolved": "http://registry.m.jd.com/lightningcss/download/lightningcss-1.30.1.tgz", "integrity": "sha1-eOl5wtWVv8uQ0qjA62Mv5sW/7V0=", "dev": true, "license": "MPL-2.0", "optional": true, "peer": true, "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1"}}, "node_modules/lightningcss-darwin-arm64": {"version": "1.30.1", "resolved": "http://registry.m.jd.com/lightningcss-darwin-arm64/download/lightningcss-darwin-arm64-1.30.1.tgz", "integrity": "sha1-PUfOXiIblWfHA5UO3yUpyko3AK4=", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "peer": true, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/lilconfig": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/lilconfig/download/lilconfig-3.1.3.tgz", "integrity": "sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "http://registry.m.jd.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz", "integrity": "sha1-7KKE910pZQeTCdwK2SVauy68FjI=", "dev": true, "license": "MIT"}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/locate-path/download/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "http://registry.m.jd.com/lodash/download/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "http://registry.m.jd.com/lodash.merge/download/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/loupe": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/loupe/download/loupe-3.2.0.tgz", "integrity": "sha1-F0Bzuo4KHQ1eQ8wIYm7YoZQDw0Q=", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://registry.m.jd.com/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/lz-string": {"version": "1.5.0", "resolved": "http://registry.m.jd.com/lz-string/download/lz-string-1.5.0.tgz", "integrity": "sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=", "dev": true, "license": "MIT", "peer": true, "bin": {"lz-string": "bin/bin.js"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "http://registry.m.jd.com/magic-string/download/magic-string-0.30.17.tgz", "integrity": "sha1-RQpElnPSRg5bvPupphkWoXFMdFM=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/merge2/download/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://registry.m.jd.com/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/min-indent": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/min-indent/download/min-indent-1.0.1.tgz", "integrity": "sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minipass": {"version": "7.1.2", "resolved": "http://registry.m.jd.com/minipass/download/minipass-7.1.2.tgz", "integrity": "sha1-k6libOXl5mvU24aEnnUV6SNApwc=", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mrmime": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/mrmime/download/mrmime-2.0.1.tgz", "integrity": "sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://registry.m.jd.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "dev": true, "license": "MIT"}, "node_modules/mz": {"version": "2.7.0", "resolved": "http://registry.m.jd.com/mz/download/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "http://registry.m.jd.com/nanoid/download/nanoid-3.3.11.tgz", "integrity": "sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "http://registry.m.jd.com/node-releases/download/node-releases-2.0.19.tgz", "integrity": "sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "resolved": "http://registry.m.jd.com/normalize-range/download/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/nwsapi": {"version": "2.2.21", "resolved": "http://registry.m.jd.com/nwsapi/download/nwsapi-2.2.21.tgz", "integrity": "sha1-jfd5cHk1Ct2iCJENjDP8TC11IMM=", "dev": true, "license": "MIT"}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://registry.m.jd.com/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-hash": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/object-hash/download/object-hash-3.0.0.tgz", "integrity": "sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "http://registry.m.jd.com/optionator/download/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/p-locate/download/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz", "integrity": "sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse5": {"version": "7.3.0", "resolved": "http://registry.m.jd.com/parse5/download/parse5-7.3.0.tgz", "integrity": "sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=", "dev": true, "license": "MIT", "dependencies": {"entities": "^6.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "http://registry.m.jd.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "resolved": "http://registry.m.jd.com/path-scurry/download/path-scurry-1.11.1.tgz", "integrity": "sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "resolved": "http://registry.m.jd.com/lru-cache/download/lru-cache-10.4.3.tgz", "integrity": "sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=", "dev": true, "license": "ISC"}, "node_modules/pathe": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/pathe/download/pathe-2.0.3.tgz", "integrity": "sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/pathval/download/pathval-2.0.1.tgz", "integrity": "sha1-iFXFooma8HLWrAXRHkYEWtDcYF0=", "dev": true, "license": "MIT", "engines": {"node": ">= 14.16"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/picocolors/download/picocolors-1.1.1.tgz", "integrity": "sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pirates": {"version": "4.0.7", "resolved": "http://registry.m.jd.com/pirates/download/pirates-4.0.7.tgz", "integrity": "sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "http://registry.m.jd.com/postcss/download/postcss-8.5.6.tgz", "integrity": "sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-import": {"version": "15.1.0", "resolved": "http://registry.m.jd.com/postcss-import/download/postcss-import-15.1.0.tgz", "integrity": "sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-js": {"version": "4.0.1", "resolved": "http://registry.m.jd.com/postcss-js/download/postcss-js-4.0.1.tgz", "integrity": "sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=", "dev": true, "license": "MIT", "dependencies": {"camelcase-css": "^2.0.1"}, "engines": {"node": "^12 || ^14 || >= 16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.4.21"}}, "node_modules/postcss-load-config": {"version": "4.0.2", "resolved": "http://registry.m.jd.com/postcss-load-config/download/postcss-load-config-4.0.2.tgz", "integrity": "sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"lilconfig": "^3.0.0", "yaml": "^2.3.4"}, "engines": {"node": ">= 14"}, "peerDependencies": {"postcss": ">=8.0.9", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"postcss": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/postcss-nested": {"version": "6.2.0", "resolved": "http://registry.m.jd.com/postcss-nested/download/postcss-nested-6.2.0.tgz", "integrity": "sha1-TC0iq18gucth4sXFkVlQeE0GgTE=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.1.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.2.14"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "resolved": "http://registry.m.jd.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz", "integrity": "sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "http://registry.m.jd.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz", "integrity": "sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=", "dev": true, "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/prelude-ls/download/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-format": {"version": "27.5.1", "resolved": "http://registry.m.jd.com/pretty-format/download/pretty-format-27.5.1.tgz", "integrity": "sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=", "dev": true, "license": "MIT", "peer": true, "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-5.2.0.tgz", "integrity": "sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "http://registry.m.jd.com/queue-microtask/download/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/react": {"version": "19.1.1", "resolved": "http://registry.m.jd.com/react/download/react-19.1.1.tgz", "integrity": "sha1-BtkUnsXgg6Z/mh45zpewagO2RK8=", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.1.1", "resolved": "http://registry.m.jd.com/react-dom/download/react-dom-19.1.1.tgz", "integrity": "sha1-Laqf9/OuOErrMOdtXuOMBG3ImJM=", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.1"}}, "node_modules/react-is": {"version": "17.0.2", "resolved": "http://registry.m.jd.com/react-is/download/react-is-17.0.2.tgz", "integrity": "sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=", "dev": true, "license": "MIT", "peer": true}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "http://registry.m.jd.com/react-refresh/download/react-refresh-0.17.0.tgz", "integrity": "sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-router": {"version": "7.7.1", "resolved": "http://registry.m.jd.com/react-router/download/react-router-7.7.1.tgz", "integrity": "sha1-wS67rRYtplWVHdeTJfIF9yONYJ4=", "license": "MIT", "dependencies": {"cookie": "^1.0.1", "set-cookie-parser": "^2.6.0"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}, "peerDependenciesMeta": {"react-dom": {"optional": true}}}, "node_modules/react-router-dom": {"version": "7.7.1", "resolved": "http://registry.m.jd.com/react-router-dom/download/react-router-dom-7.7.1.tgz", "integrity": "sha1-RW0QU92l4cEyiitrI1n2ocRwcvw=", "license": "MIT", "dependencies": {"react-router": "7.7.1"}, "engines": {"node": ">=20.0.0"}, "peerDependencies": {"react": ">=18", "react-dom": ">=18"}}, "node_modules/read-cache": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/read-cache/download/read-cache-1.0.0.tgz", "integrity": "sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=", "dev": true, "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "http://registry.m.jd.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/redent": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/redent/download/redent-3.0.0.tgz", "integrity": "sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=", "dev": true, "license": "MIT", "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "http://registry.m.jd.com/resolve/download/resolve-1.22.10.tgz", "integrity": "sha1-tmPoP/sJu/I4aURza6roAwKbizk=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/reusify/download/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rollup": {"version": "4.46.2", "resolved": "http://registry.m.jd.com/rollup/download/rollup-4.46.2.tgz", "integrity": "sha1-CbGkXYEeJtCb7WPcPs+2gxwWzjI=", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.46.2", "@rollup/rollup-android-arm64": "4.46.2", "@rollup/rollup-darwin-arm64": "4.46.2", "@rollup/rollup-darwin-x64": "4.46.2", "@rollup/rollup-freebsd-arm64": "4.46.2", "@rollup/rollup-freebsd-x64": "4.46.2", "@rollup/rollup-linux-arm-gnueabihf": "4.46.2", "@rollup/rollup-linux-arm-musleabihf": "4.46.2", "@rollup/rollup-linux-arm64-gnu": "4.46.2", "@rollup/rollup-linux-arm64-musl": "4.46.2", "@rollup/rollup-linux-loongarch64-gnu": "4.46.2", "@rollup/rollup-linux-ppc64-gnu": "4.46.2", "@rollup/rollup-linux-riscv64-gnu": "4.46.2", "@rollup/rollup-linux-riscv64-musl": "4.46.2", "@rollup/rollup-linux-s390x-gnu": "4.46.2", "@rollup/rollup-linux-x64-gnu": "4.46.2", "@rollup/rollup-linux-x64-musl": "4.46.2", "@rollup/rollup-win32-arm64-msvc": "4.46.2", "@rollup/rollup-win32-ia32-msvc": "4.46.2", "@rollup/rollup-win32-x64-msvc": "4.46.2", "fsevents": "~2.3.2"}}, "node_modules/rrweb-cssom": {"version": "0.8.0", "resolved": "http://registry.m.jd.com/rrweb-cssom/download/rrweb-cssom-0.8.0.tgz", "integrity": "sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=", "dev": true, "license": "MIT"}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/run-parallel/download/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://registry.m.jd.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true, "license": "MIT"}, "node_modules/saxes": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/saxes/download/saxes-6.0.0.tgz", "integrity": "sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=v12.22.7"}}, "node_modules/scheduler": {"version": "0.26.0", "resolved": "http://registry.m.jd.com/scheduler/download/scheduler-0.26.0.tgz", "integrity": "sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=", "license": "MIT"}, "node_modules/semver": {"version": "6.3.1", "resolved": "http://registry.m.jd.com/semver/download/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/set-cookie-parser": {"version": "2.7.1", "resolved": "http://registry.m.jd.com/set-cookie-parser/download/set-cookie-parser-2.7.1.tgz", "integrity": "sha1-MBbxUAciAt++kPre4FNXPMidKUM=", "license": "MIT"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/siginfo": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/siginfo/download/siginfo-2.0.0.tgz", "integrity": "sha1-MudscLeXJOO7Vny51UPrhYzPrzA=", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/signal-exit/download/signal-exit-4.1.0.tgz", "integrity": "sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/sirv": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/sirv/download/sirv-3.0.1.tgz", "integrity": "sha1-MqhEeUZVtyf54oZ7d34AYPvge/M=", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">=18"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/source-map-js/download/source-map-js-1.2.1.tgz", "integrity": "sha1-HOVlD93YerwJnto33P8CTCZnrkY=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/stackback": {"version": "0.0.2", "resolved": "http://registry.m.jd.com/stackback/download/stackback-0.0.2.tgz", "integrity": "sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=", "dev": true, "license": "MIT"}, "node_modules/std-env": {"version": "3.9.0", "resolved": "http://registry.m.jd.com/std-env/download/std-env-3.9.0.tgz", "integrity": "sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=", "dev": true, "license": "MIT"}, "node_modules/string-width": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/string-width/download/string-width-5.1.2.tgz", "integrity": "sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "resolved": "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/string-width-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "7.1.0", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-7.1.0.tgz", "integrity": "sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi/node_modules/ansi-regex": {"version": "6.1.0", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-6.1.0.tgz", "integrity": "sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/strip-indent": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/strip-indent/download/strip-indent-3.0.0.tgz", "integrity": "sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=", "dev": true, "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-literal": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/strip-literal/download/strip-literal-3.0.0.tgz", "integrity": "sha1-zpxFKpGgryh27Rrk5YNTmjU98/w=", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^9.0.1"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/strip-literal/node_modules/js-tokens": {"version": "9.0.1", "resolved": "http://registry.m.jd.com/js-tokens/download/js-tokens-9.0.1.tgz", "integrity": "sha1-LsQ5ZGWENSlvZ2GzThBnHC2VJ/Q=", "dev": true, "license": "MIT"}, "node_modules/sucrase": {"version": "3.35.0", "resolved": "http://registry.m.jd.com/sucrase/download/sucrase-3.35.0.tgz", "integrity": "sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/symbol-tree": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/symbol-tree/download/symbol-tree-3.2.4.tgz", "integrity": "sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=", "dev": true, "license": "MIT"}, "node_modules/tabbable": {"version": "6.2.0", "resolved": "http://registry.m.jd.com/tabbable/download/tabbable-6.2.0.tgz", "integrity": "sha1-cy+2K8AXXPzsJXMwvhh9z7ofO5c=", "license": "MIT"}, "node_modules/tailwindcss": {"version": "3.4.17", "resolved": "http://registry.m.jd.com/tailwindcss/download/tailwindcss-3.4.17.tgz", "integrity": "sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=", "dev": true, "license": "MIT", "dependencies": {"@alloc/quick-lru": "^5.2.0", "arg": "^5.0.2", "chokidar": "^3.6.0", "didyoumean": "^1.2.2", "dlv": "^1.1.3", "fast-glob": "^3.3.2", "glob-parent": "^6.0.2", "is-glob": "^4.0.3", "jiti": "^1.21.6", "lilconfig": "^3.1.3", "micromatch": "^4.0.8", "normalize-path": "^3.0.0", "object-hash": "^3.0.0", "picocolors": "^1.1.1", "postcss": "^8.4.47", "postcss-import": "^15.1.0", "postcss-js": "^4.0.1", "postcss-load-config": "^4.0.2", "postcss-nested": "^6.2.0", "postcss-selector-parser": "^6.1.2", "resolve": "^1.22.8", "sucrase": "^3.35.0"}, "bin": {"tailwind": "lib/cli.js", "tailwindcss": "lib/cli.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/tailwindcss/node_modules/jiti": {"version": "1.21.7", "resolved": "http://registry.m.jd.com/jiti/download/jiti-1.21.7.tgz", "integrity": "sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=", "dev": true, "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/thenify": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/thenify/download/thenify-3.3.1.tgz", "integrity": "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "resolved": "http://registry.m.jd.com/thenify-all/download/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "dev": true, "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/tinybench": {"version": "2.9.0", "resolved": "http://registry.m.jd.com/tinybench/download/tinybench-2.9.0.tgz", "integrity": "sha1-EDyfi6bXI3pHq23R3P93JRhjQms=", "dev": true, "license": "MIT"}, "node_modules/tinyexec": {"version": "0.3.2", "resolved": "http://registry.m.jd.com/tinyexec/download/tinyexec-0.3.2.tgz", "integrity": "sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=", "dev": true, "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "http://registry.m.jd.com/tinyglobby/download/tinyglobby-0.2.14.tgz", "integrity": "sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinyglobby/node_modules/fdir": {"version": "6.4.6", "resolved": "http://registry.m.jd.com/fdir/download/fdir-6.4.6.tgz", "integrity": "sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/tinyglobby/node_modules/picomatch": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.3.tgz", "integrity": "sha1-eWx2E20e6tcV2x57rXhd7daVoEI=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/tinypool": {"version": "1.1.1", "resolved": "http://registry.m.jd.com/tinypool/download/tinypool-1.1.1.tgz", "integrity": "sha1-BZ8tBCvTdWf7wBfT1Ca90qJhJZE=", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}}, "node_modules/tinyrainbow": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/tinyrainbow/download/tinyrainbow-2.0.0.tgz", "integrity": "sha1-lQmyFiQ2MV6A4+7g/M5EdNJEQpQ=", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/tinyspy/download/tinyspy-4.0.3.tgz", "integrity": "sha1-0dDwYC9MFfGq4IOjTW0N8zY7G1I=", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tldts": {"version": "6.1.86", "resolved": "http://registry.m.jd.com/tldts/download/tldts-6.1.86.tgz", "integrity": "sha1-CH4FVbMblyXuSMp+d+3FYRXNgvc=", "dev": true, "license": "MIT", "dependencies": {"tldts-core": "^6.1.86"}, "bin": {"tldts": "bin/cli.js"}}, "node_modules/tldts-core": {"version": "6.1.86", "resolved": "http://registry.m.jd.com/tldts-core/download/tldts-core-6.1.86.tgz", "integrity": "sha1-qT5u2dUFy1TFQs5D/rFMc5EyZdg=", "dev": true, "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/totalist": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/totalist/download/totalist-3.0.1.tgz", "integrity": "sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tough-cookie": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/tough-cookie/download/tough-cookie-5.1.2.tgz", "integrity": "sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tldts": "^6.1.32"}, "engines": {"node": ">=16"}}, "node_modules/tr46": {"version": "5.1.1", "resolved": "http://registry.m.jd.com/tr46/download/tr46-5.1.1.tgz", "integrity": "sha1-lq6GfN24/bZKScwwWajUKLzyOMo=", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz", "integrity": "sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-interface-checker": {"version": "0.1.13", "resolved": "http://registry.m.jd.com/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz", "integrity": "sha1-eE/T1nlyK8EDsbS4AwvN212yppk=", "dev": true, "license": "Apache-2.0"}, "node_modules/tslib": {"version": "2.8.1", "resolved": "http://registry.m.jd.com/tslib/download/tslib-2.8.1.tgz", "integrity": "sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=", "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "http://registry.m.jd.com/type-check/download/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/typescript": {"version": "5.7.3", "resolved": "http://registry.m.jd.com/typescript/download/typescript-5.7.3.tgz", "integrity": "sha1-kZtEp9u4WDqbhW0WK+JKVL+ABz4=", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/typescript-eslint": {"version": "8.39.0", "resolved": "http://registry.m.jd.com/typescript-eslint/download/typescript-eslint-8.39.0.tgz", "integrity": "sha1-sZwaklz4Vmgxrjh10oge4jSYCKU=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/eslint-plugin": "8.39.0", "@typescript-eslint/parser": "8.39.0", "@typescript-eslint/typescript-estree": "8.39.0", "@typescript-eslint/utils": "8.39.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "http://registry.m.jd.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz", "integrity": "sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/use-sync-external-store": {"version": "1.5.0", "resolved": "http://registry.m.jd.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz", "integrity": "sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=", "license": "MIT", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "license": "MIT"}, "node_modules/vite": {"version": "6.3.5", "resolved": "http://registry.m.jd.com/vite/download/vite-6.3.5.tgz", "integrity": "sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-node": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/vite-node/download/vite-node-3.2.4.tgz", "integrity": "sha1-82dtlMSvHnaJjBYsknKLymX3uwc=", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.4.1", "es-module-lexer": "^1.7.0", "pathe": "^2.0.3", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vite/node_modules/fdir": {"version": "6.4.6", "resolved": "http://registry.m.jd.com/fdir/download/fdir-6.4.6.tgz", "integrity": "sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/vite/node_modules/picomatch": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.3.tgz", "integrity": "sha1-eWx2E20e6tcV2x57rXhd7daVoEI=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/vitest": {"version": "3.2.4", "resolved": "http://registry.m.jd.com/vitest/download/vitest-3.2.4.tgz", "integrity": "sha1-Bje5A6150VOaJbw0wO1UtcZ3Auo=", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/expect": "3.2.4", "@vitest/mocker": "3.2.4", "@vitest/pretty-format": "^3.2.4", "@vitest/runner": "3.2.4", "@vitest/snapshot": "3.2.4", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "debug": "^4.4.1", "expect-type": "^1.2.1", "magic-string": "^0.30.17", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tinypool": "^1.1.1", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0", "vite-node": "3.2.4", "why-is-node-running": "^2.3.0"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/debug": "^4.1.12", "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "@vitest/browser": "3.2.4", "@vitest/ui": "3.2.4", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/debug": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/vitest/node_modules/picomatch": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-4.0.3.tgz", "integrity": "sha1-eWx2E20e6tcV2x57rXhd7daVoEI=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/w3c-xmlserializer": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/w3c-xmlserializer/download/w3c-xmlserializer-5.0.0.tgz", "integrity": "sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/webidl-conversions": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/webidl-conversions/download/webidl-conversions-7.0.0.tgz", "integrity": "sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "node_modules/whatwg-encoding": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/whatwg-encoding/download/whatwg-encoding-3.1.1.tgz", "integrity": "sha1-0PTvdpkF1CbhaI8+NDgambYLduU=", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=18"}}, "node_modules/whatwg-mimetype": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/whatwg-mimetype/download/whatwg-mimetype-4.0.0.tgz", "integrity": "sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/whatwg-url": {"version": "14.2.0", "resolved": "http://registry.m.jd.com/whatwg-url/download/whatwg-url-14.2.0.tgz", "integrity": "sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=", "dev": true, "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/why-is-node-running": {"version": "2.3.0", "resolved": "http://registry.m.jd.com/why-is-node-running/download/why-is-node-running-2.3.0.tgz", "integrity": "sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "http://registry.m.jd.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "8.1.0", "resolved": "http://registry.m.jd.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz", "integrity": "sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "resolved": "http://registry.m.jd.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/emoji-regex": {"version": "8.0.0", "resolved": "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi-cjs/node_modules/string-width": {"version": "4.2.3", "resolved": "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi-cjs/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-6.2.1.tgz", "integrity": "sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/ws": {"version": "8.18.3", "resolved": "http://registry.m.jd.com/ws/download/ws-8.18.3.tgz", "integrity": "sha1-tWuIq//eYnkcY5FwQAyT3LDJVHI=", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/xml-name-validator/download/xml-name-validator-5.0.0.tgz", "integrity": "sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18"}}, "node_modules/xmlchars": {"version": "2.2.0", "resolved": "http://registry.m.jd.com/xmlchars/download/xmlchars-2.2.0.tgz", "integrity": "sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=", "dev": true, "license": "MIT"}, "node_modules/yallist": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/yallist/download/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.8.1", "resolved": "http://registry.m.jd.com/yaml/download/yaml-2.8.1.tgz", "integrity": "sha1-GHCqArYx9+gyi5P4vFdPrF1sTXk=", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14.6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "http://registry.m.jd.com/yocto-queue/download/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zustand": {"version": "5.0.7", "resolved": "http://registry.m.jd.com/zustand/download/zustand-5.0.7.tgz", "integrity": "sha1-4yU2ToLJkqhL84bYRFqn8YDEUNw=", "license": "MIT", "engines": {"node": ">=12.20.0"}, "peerDependencies": {"@types/react": ">=18.0.0", "immer": ">=9.0.6", "react": ">=18.0.0", "use-sync-external-store": ">=1.2.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "immer": {"optional": true}, "react": {"optional": true}, "use-sync-external-store": {"optional": true}}}}}