{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "immer": "^10.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.7.1", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/js": "^9.19.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.0.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vitest": "^3.2.4"}}