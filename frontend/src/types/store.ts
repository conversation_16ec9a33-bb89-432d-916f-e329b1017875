import { Task, Message, SystemConfig } from './api'

// 任务Store类型
export interface TaskState {
  tasks: Task[]
  currentTask: Task | null
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
  }
  filters: {
    search: string
    dateRange?: [Date, Date]
  }
  selectedTaskIds: string[]
}

export interface TaskActions {
  fetchTasks: (params?: any) => Promise<void>
  selectTask: (taskId: string) => Promise<any>
  createTask: (title: string) => Promise<Task>
  updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>
  deleteTask: (taskId: string) => Promise<void>
  selectMultipleTasks: (taskIds: string[]) => void
  deleteMultipleTasks: (taskIds: string[]) => Promise<void>
  setSearch: (search: string) => void
  setFilters: (filters: Partial<TaskState['filters']>) => void
  clearFilters: () => void
  reset: () => void
}

export type TaskStore = TaskState & TaskActions

// 聊天Store类型
export interface ChatState {
  messages: Message[]
  isConnected: boolean
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  isTyping: boolean
  inputContent: string
  selectedContext: string | null
  isReceivingResponse: boolean
  currentResponseId: string | null
  error: string | null
}

export interface ChatActions {
  sendMessage: (content: string, contextHtml?: string) => void
  addMessage: (message: Message) => void
  updateMessage: (messageId: string, updates: Partial<Message>) => void
  deleteMessage: (messageId: string) => void
  connect: (taskId: string) => Promise<void>
  disconnect: () => void
  reconnect: () => void
  setInputContent: (content: string) => void
  setSelectedContext: (context: string | null) => void
  startResponse: (responseId: string) => void
  appendResponseChunk: (responseId: string, chunk: string) => void
  finishResponse: (responseId: string) => void
  loadMessages: (messages: Message[]) => void
  loadTaskMessages: (taskId: string) => Promise<void>
  clearMessages: () => void
  reset: () => void
}

export type ChatStore = ChatState & ChatActions

// 报告Store类型
export interface ReportState {
  reportHtml: string
  reportPath: string | null
  selectedArea: string | null
  selectionRange: Range | null
  isGenerating: boolean
  generationProgress: number
  zoomLevel: number
  viewMode: 'fit' | 'width' | 'actual'
  error: string | null
}

export interface ReportActions {
  loadReport: (reportPath: string) => Promise<void>
  updateReport: (html: string) => void
  clearReport: () => void
  selectArea: (html: string, range?: Range) => void
  clearSelection: () => void
  startGeneration: () => void
  updateProgress: (progress: number) => void
  finishGeneration: (reportPath: string) => void
  setZoomLevel: (level: number) => void
  setViewMode: (mode: 'fit' | 'width' | 'actual') => void
  exportToPDF: () => Promise<void>
  reset: () => void
}

export type ReportStore = ReportState & ReportActions

// 配置Store类型
export interface ConfigState {
  config: SystemConfig
  loading: boolean
  error: string | null
  theme: 'light' | 'dark' | 'auto'
  language: 'zh-CN' | 'en-US'
  sidebarCollapsed: boolean
  chatPanelWidth: number
  reportZoomLevel: number
}

export interface ConfigActions {
  fetchConfig: () => Promise<void>
  updateConfig: (config: Partial<SystemConfig>) => Promise<void>
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  setLanguage: (language: 'zh-CN' | 'en-US') => void
  toggleSidebar: () => void
  setChatPanelWidth: (width: number) => void
  setReportZoomLevel: (level: number) => void
  reset: () => void
}

export type ConfigStore = ConfigState & ConfigActions

// UI Store类型
export interface UIState {
  activePanel: 'sidebar' | 'report' | 'chat'
  sidebarCollapsed: boolean
  chatPanelWidth: number
  reportZoomLevel: number
  theme: 'light' | 'dark'
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
}

export interface UIActions {
  setActivePanel: (panel: 'sidebar' | 'report' | 'chat') => void
  toggleSidebar: () => void
  setChatPanelWidth: (width: number) => void
  setReportZoomLevel: (level: number) => void
  setTheme: (theme: 'light' | 'dark') => void
  updateBreakpoint: (breakpoint: 'mobile' | 'tablet' | 'desktop') => void
}

export type UIStore = UIState & UIActions

// 全局状态类型
export interface GlobalState {
  task: TaskStore
  chat: ChatStore
  report: ReportStore
  config: ConfigStore
  ui: UIStore
}
