import React, { useState, useEffect } from 'react'
import clsx from 'clsx'
import { useTaskStore } from '../../../stores'

interface ReportViewerProps {
  className?: string
}

const ReportViewer: React.FC<ReportViewerProps> = ({
  className = ''
}) => {
  const { currentTask } = useTaskStore()
  const [selectedText, setSelectedText] = useState('')
  const [reportContent, setReportContent] = useState('')

  // 当任务切换时清除选中的文本并加载新的报告内容
  useEffect(() => {
    setSelectedText('')
    window.getSelection()?.removeAllRanges()

    // 根据当前任务加载报告内容
    if (currentTask?.report_file_path) {
      // 这里应该调用API获取报告内容
      // 暂时使用模拟数据，但根据任务ID生成不同的内容
      const mockContent = generateMockReportContent(currentTask.id, currentTask.title)
      setReportContent(mockContent)
    } else {
      setReportContent('')
    }
  }, [currentTask?.id, currentTask?.report_file_path, currentTask?.title])

  // 生成模拟报告内容的函数
  const generateMockReportContent = (taskId: string, taskTitle: string) => {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
          ${taskTitle} - 数据分析报告
        </h1>

        <div style="background: #f3f4f6; padding: 15px; border-radius: 8px; margin: 20px 0;">
          <h2 style="color: #374151; margin-top: 0;">任务ID: ${taskId}</h2>
          <p style="line-height: 1.6; color: #4b5563;">
            本报告基于任务 "${taskTitle}" 的数据分析结果，展示了关键业务指标的趋势和洞察。
            每个任务都有其独特的分析内容和结论。
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">关键发现</h2>
        <ul style="line-height: 1.8; color: #4b5563;">
          <li>任务 ${taskId} 的数据处理已完成</li>
          <li>分析结果显示良好的数据质量</li>
          <li>建议继续监控相关指标</li>
          <li>后续可进行深度分析</li>
        </ul>

        <div style="background: #ecfdf5; border-left: 4px solid #10b981; padding: 15px; margin: 20px 0;">
          <h3 style="color: #065f46; margin-top: 0;">任务状态</h3>
          <p style="color: #047857; margin-bottom: 0;">
            任务 "${taskTitle}" 已成功完成分析，报告已生成。
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">数据可视化</h2>
        <div style="background: #f9fafb; border: 1px solid #e5e7eb; border-radius: 8px; padding: 20px; text-align: center; margin: 20px 0;">
          <div style="width: 100%; height: 200px; background: linear-gradient(45deg, #3b82f6, #10b981); border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
            📊 ${taskTitle} 数据图表
          </div>
          <p style="margin-top: 10px; color: #6b7280; font-size: 14px;">
            任务 ${taskId} 的数据可视化展示
          </p>
        </div>

        <h2 style="color: #374151; margin-top: 30px;">结论与建议</h2>
        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 15px; margin: 20px 0;">
          <ol style="line-height: 1.8; color: #92400e; margin: 0;">
            <li>任务 "${taskTitle}" 执行成功</li>
            <li>数据分析结果符合预期</li>
            <li>建议定期更新分析内容</li>
            <li>可考虑扩展分析维度</li>
          </ol>
        </div>
      </div>
    `
  }



  const handleTextSelection = () => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString().trim())
      // 这里可以将选中的文本传递给聊天组件作为上下文
      console.log('Selected text:', selection.toString().trim())
    }
  }

  const clearSelection = () => {
    setSelectedText('')
    window.getSelection()?.removeAllRanges()
  }

  if (!currentTask) {
    return (
      <div className={clsx('flex flex-col items-center justify-center h-full bg-gray-50', className)}>
        <div className="text-center max-w-md">
          <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-3">
            选择任务查看报告
          </h3>
          <p className="text-gray-600 leading-relaxed">
            请从左侧任务列表中选择一个任务，或创建新任务开始生成报告。
            生成的报告将在此处显示，您可以选择报告中的内容与AI进行讨论。
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className={clsx('flex flex-col h-full bg-white', className)}>
      {/* 选中文本提示 */}
      {selectedText && (
        <div className="bg-blue-50 border-b border-blue-200 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <span className="text-sm font-medium text-blue-800">
                已选择文本: "{selectedText.substring(0, 50)}{selectedText.length > 50 ? '...' : ''}"
              </span>
            </div>
            <button
              onClick={clearSelection}
              className="text-blue-600 hover:text-blue-800 text-sm"
            >
              清除选择
            </button>
          </div>
        </div>
      )}

      {/* 报告内容 */}
      <div
        className="flex-1 overflow-auto"
        style={{
          display: 'grid',
          placeItems: 'start center',
          padding: '24px',
          width: '100%'
        }}
      >
        {currentTask.report_file_path && reportContent ? (
          <div
            style={{
              width: '100%',
              maxWidth: '800px',
              margin: '0 auto'
            }}
          >
            <div
              className="prose prose-lg"
              style={{
                maxWidth: 'none',
                width: '100%'
              }}
              onMouseUp={handleTextSelection}
              dangerouslySetInnerHTML={{ __html: reportContent }}
            />
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              报告生成中
            </h3>
            <p className="text-gray-600 max-w-md">
              当前任务还没有生成报告。请在右侧聊天区域描述您的需求，AI将为您生成详细的数据分析报告。
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportViewer
