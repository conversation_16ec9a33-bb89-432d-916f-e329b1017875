import React, { useEffect } from 'react'
import clsx from 'clsx'
import { useChatStore, useTaskStore } from '../../../stores'
import ChatHeader from '../ChatHeader'
import MessageList from '../MessageList'
import ChatInput from '../ChatInput'
import ConnectionStatus from '../ConnectionStatus'

interface ChatInterfaceProps {
  className?: string
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  className = ''
}) => {
  const { currentTask } = useTaskStore()
  const {
    messages,
    isConnected,
    connectionStatus,
    selectedContext,
    connect,
    sendMessage,
    setSelectedContext,
    clearMessages
  } = useChatStore()

  // 当选择任务时自动连接WebSocket
  useEffect(() => {
    if (currentTask?.id) {
      connect(currentTask.id)
    } else {
      // 如果没有选择任务，清理聊天数据
      clearMessages()
    }
  }, [currentTask?.id, connect, clearMessages])

  const handleSendMessage = (content: string, contextHtml?: string) => {
    if (content.trim()) {
      sendMessage(content, contextHtml)
    }
  }

  const handleContextClear = () => {
    setSelectedContext(null)
  }

  return (
    <div
      className={clsx('flex flex-col h-full bg-white overflow-hidden', className)}
      style={{
        width: '300px',
        maxWidth: '300px',
        minWidth: '300px'
      }}
    >
      {/* 聊天头部 */}
      <ChatHeader
        taskTitle={currentTask?.title || '未选择任务'}
        isConnected={isConnected}
        connectionStatus={connectionStatus}
      />

      {/* 连接状态提示 */}
      <ConnectionStatus
        isConnected={isConnected}
        connectionStatus={connectionStatus}
        taskId={currentTask?.id}
      />

      {/* 消息列表 */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList
          messages={messages}
          loading={connectionStatus === 'connecting'}
        />
      </div>

      {/* 输入区域 */}
      <div className="border-t border-gray-200">
        <ChatInput
          onSend={handleSendMessage}
          disabled={!isConnected || !currentTask}
          placeholder={
            !currentTask
              ? '请先选择一个任务...'
              : !isConnected
              ? '连接中...'
              : '输入消息...'
          }
          selectedContext={selectedContext || undefined}
          onContextClear={handleContextClear}
        />
      </div>
    </div>
  )
}

export default ChatInterface
